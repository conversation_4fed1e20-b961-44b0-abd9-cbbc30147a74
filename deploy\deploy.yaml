apiVersion: apps/v1
kind: Deployment
metadata:
  name: klynx-uat
  namespace: policeinnopolis-p6
  labels:
    app: klynx-uat
spec:
  selector:
    matchLabels:
      app: klynx-uat
  template:
    metadata:
      labels:
        app: klynx-uat
    spec:
      nodeSelector:
        kubernetes.io/hostname: k8sserver3
      containers:
        - name: klynx-uat
          image: regis.pointit.co.th/infra/klynx-app-dev:0.0.2
          command: ["sleep"]
          args: ["infinity"]
          imagePullPolicy: IfNotPresent
            # env:
            # - name: KAFKA_BROKER
            #  value: kafka.eventdriven.svc.cluster.local:9092
          ports:
            - name: http
              containerPort: 3000
          volumeMounts:
            - name: src-code
              mountPath: /app
            - name: tmp
              mountPath: /tmp
          securityContext:
            privileged: true
            runAsUser: 0
      volumes:
        - name: src-code
          hostPath:
            path: /home/<USER>/klynx
            type: Directory
        - name: tmp
          emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: klynx-uat
  namespace: policeinnopolis-p6
spec:
  selector:
    app: klynx-uat
  ports:
    - name: http
      port: 3001
      targetPort: 3000
      protocol: TCP
  type: ClusterIP
