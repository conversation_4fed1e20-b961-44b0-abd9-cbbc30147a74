<script setup lang="ts">
import type { GetUsersResponse, UserRow } from '~/types/'

const { t } = useI18n()
const groups = ref<any[]>([])  
// Columns สำหรับ UTable (ใช้ computed เพื่อ reactive กับ i18n)
const columns = computed(() => [
  { accessorKey: 'avatar', header: '' },
  { accessorKey: 'order', header: t('systemUsers.table.order') },
  { accessorKey: 'username', header: t('systemUsers.table.username') },
  { accessorKey: 'fullname', header: t('systemUsers.table.fullname') },
  { accessorKey: 'phone', header: t('systemUsers.table.phone') },
  { accessorKey: 'email', header: t('systemUsers.table.email') },
  { accessorKey: 'status', header: t('systemUsers.table.status') },
  { accessorKey: 'role', header: t('systemUsers.table.role') },
  { accessorKey: 'createdAt', header: t('systemUsers.table.createdAt') },
])

// State
const q = ref('')
const page = ref(1)
const pageSize = ref(10)
const pending = ref(false)
const userResponse = ref<{ users: UserRow[]; total: number }>({ users: [], total: 0 })

const users = computed(() => userResponse.value.users)
const total = computed(() => userResponse.value.total)

// Map API → UserRow
function mapUserToRow(u: any, index: number): UserRow {
  return {
    id: u.id,
    username: u.username,
    fullname: `${u.firstName ?? ''} ${u.lastName ?? ''}`.trim(),
    phone: u.phone || '',
    email: u.email || '',
    status: u.enabled ?? false,
    role: u.role || '',
    createdAt: u.createdAt || '',
    order: (page.value - 1) * pageSize.value + (index + 1),
  }
}

// Fetch Users
const fetchUsers = async () => {
  try {
    pending.value = true
    const resp = await useApi<GetUsersResponse>('/kapi/users', {
      params: { page: page.value, perPage: pageSize.value, q: q.value },
    })
    userResponse.value = {
      users: (resp.details || []).map((u, idx) => mapUserToRow(u, idx)),
      total: resp.pagination?.totalRecords || resp.details?.length || 0,
    }
  } catch (err) {
    console.error('Error fetching users:', err)
  } finally {
    pending.value = false
  }
}

onMounted(fetchUsers)
watch([page, q], fetchUsers)
</script>

<template>
  <UDashboardPanel id="users">
    <template #header>
      <UDashboardNavbar :title="t('systemUsers.title')">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <CustomersAddModal />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="flex flex-wrap items-center justify-between gap-1.5 mb-4">
        <UInput
          v-model="q"
          class="max-w-sm"
          icon="i-lucide-search"
          :placeholder="t('systemUsers.searchPlaceholder')"
        />
      </div>

      <UTable
        :columns="columns"
        :data="users"
        :loading="pending"
        class="border rounded-lg"
      >
        <template #cell.status="{ row }">
          <UBadge :color="row.original.status ? 'success' : 'error'">
            {{ row.original.status ? t('systemUsers.status.active') : t('systemUsers.status.inactive') }}
          </UBadge>
        </template>
      </UTable>

      <div class="flex items-center justify-between gap-3 border-t border-default pt-4 mt-4">
        <div class="text-sm text-muted">
          {{ t('systemUsers.pagination.showing') }}
          {{ (page-1)*pageSize+1 }} -
          {{ Math.min(page*pageSize, total) }}
          {{ t('systemUsers.pagination.of') }} {{ total }}
          {{ t('systemUsers.pagination.users') }}
        </div>
        <UPagination
          v-model:page="page"
          :items-per-page="pageSize"
          :total="total"
        />
      </div>
    </template>
  </UDashboardPanel>
</template>
