// app/stores/auth.ts
import { defineStore } from 'pinia'
import type { UserData, AuthState } from '~/types'

export const useAuthStore = defineStore('auth', {
  state: (): AuthState & { ready: boolean } => ({
    isAuthenticated: false,
    user: null,
    ready: false   // ✅ เพิ่ม flag สำหรับบอกว่า init auth เสร็จ
  }),

  actions: {
    setAuthenticated(value: boolean) {
      this.isAuthenticated = value
      this.ready = true       // ✅ auth ถูกเซ็ตแล้ว
    },

    setUser(user: UserData) {
      this.user = user
    },

    logout() {
      this.isAuthenticated = false
      this.user = null
      this.ready = true        // ✅ ถือว่า store พร้อมแล้ว แม้ logout
    }
  }
})
