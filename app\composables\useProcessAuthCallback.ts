// app/composables/useProcessAuthCallback.ts
import { useAuthStore } from '~/stores/auth'

export function useProcessAuthCallback() {
    const route = useRoute()
    const auth = useAuthStore()

    return async () => {
        // รองรับทั้ง query และ hash (#token=..)
        const params = new URLSearchParams(
            route.fullPath.includes('#')
                ? route.fullPath.split('#')[1]
                : route.fullPath.split('?')[1] || ''
        )

        const token = (route.query.token as string) || params.get('token') || ''
        const refreshToken = (route.query.refresh_token as string) || params.get('refresh_token') || ''
        const userRaw = (route.query.user as string) || params.get('user')
        const user = userRaw ? JSON.parse(userRaw) : null

        if (!token) return false

            ; (auth as any).refreshToken = refreshToken
        auth.user = user
        auth.isAuthenticated = true
        auth.ready = true

        // เคลียร์ query/hash เพื่อไม่ให้ค้าง
        if (process.client) {
            const clean = window.location.pathname
            window.history.replaceState({}, '', clean)
        }
        return true
    }
}
