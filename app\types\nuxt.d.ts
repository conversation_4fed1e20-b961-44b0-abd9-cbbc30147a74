// app/types/nuxt.d.ts
// ใช้สำหรับกำหนด Type ของ NuxtApp และ Runtime Config แบบ global เพื่อให้ TypeScript รู้จักตัวแปรที่ใช้ใน Nuxt
import type Keycloak from 'keycloak-js'
import type { Socket } from 'socket.io-client'
import type { MqttClient } from 'mqtt'

declare module '#app' {
  interface NuxtApp {
    // จากไฟล์ keycloak และ apiFetch
    $keycloak: Keycloak
    $apiFetch: <T = unknown>(url: string, opts?: Record<string, unknown>) => Promise<T>
    $socket: Socket

    // จากไฟล์ mqtt
    $mqtt: MqttClient
  }

  // Runtime Config ควรอยู่ที่นี่
  interface PublicRuntimeConfig {
    apiBase: string
    baseURL: string
    kcIssuerURI: string
    kcRealm: string
    kcClientId: string
    mqttURL: string
    mqttUsername: string
    mqttPassword: string
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $keycloak: Keycloak
  }
}

declare global {
  interface Window {
    __SESSION__?: {
      token?: string;
      refreshToken?: string;
      payload?: any;
    };
  }
}

// บรรทัดนี้สำคัญมาก! เพื่อให้ TypeScript มองว่าเป็น module
export { }
