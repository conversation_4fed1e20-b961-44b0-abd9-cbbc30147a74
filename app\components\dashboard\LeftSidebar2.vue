<script>
import VideoFeedItem from './VideoFeedItem.vue';

export default {
  name: 'LeftSidebar',
  components: {
    VideoFeedItem,
  },
  data() {
    return {
      videoFeeds: [
        { id: 1, name: 'กล้องที่ 1', thumbnail: '/images/logo-dark.webp' },
        { id: 2, name: 'กล้องที่ 2', thumbnail: '/images/logo-dark.webp' },
        // ... more feeds
      ],
    };
  },
};
</script>


<template>
  <aside class="left-sidebar">
    <div class="sidebar-header">
      <h3>ตรวจจับเหตุการณ์ที่ต้องการป้องกัน</h3>
    </div>
    <div class="video-feed-list">
      <VideoFeedItem
        v-for="feed in videoFeeds"
        :key="feed.id"
        :feed="feed"
      />
    </div>
  </aside>
</template>



<style scoped>
.left-sidebar {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto; /* Scroll if content overflows */
}

.sidebar-header h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.video-feed-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
</style>