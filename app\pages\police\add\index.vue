<script setup lang="ts">
import * as z from 'zod'
import type { FormSubmitEvent } from '@nuxt/ui'
import { string } from 'zod/v4'
import { useAuthStore } from "~/stores/auth";
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const { t } = useI18n()
const authStore = useAuthStore();
console.log('authStore.user', authStore.user);


const schema = z.object({
    personType: z.enum(['1', '2'], { required_error: 'Please select a person type' }),
    generalPersonType32: z.string().optional(),
    crimePersonType23: z.string().optional(),
    typeNoti: z.array(z.string()).default([]),
    idType: z.enum(['idCard', 'passport']),
    idNumber: z.string().min(13, 'ID Card number must be 13 digits').max(13, 'ID Card number must be 13 digits').optional(),
    passport: z.string().optional(),
    prefix: z.string().min(1, 'Prefix is required'),
    firstName: z.string().min(2, 'First name is too short'),
    lastName: z.string().min(2, 'Last name is too short'),
    nickname: z.string().optional(),
    dateOfBirth: z.string().refine((val) => !isNaN(new Date(val).getTime()), { message: 'Invalid date' }),
    age: z.number().min(0, 'Age cannot be negative').max(150, 'Age seems too high').optional(),
    gender: z.string().min(1, 'Gender is required'),
    policeRegion: z.string().optional(),
    provincial: z.string().optional(),
    station: z.string().optional(),
    fatherName: z.string().optional(),
    fatherIdcard: z.string().optional(),
    motherName: z.string().optional(),
    motherIdcard: z.string().optional(),
    maritalStatus: z.string().optional(),
    deathStatus: z.string().optional(),
    dateOfDeath: z.string().optional(),
})

type Schema = z.output<typeof schema>

const state = reactive<Partial<Schema>>({
    personType: '1',
    generalPersonType32: undefined,
    crimePersonType23: undefined,
    typeNoti: [],
    idType: 'idCard',
    passport: undefined,
    idNumber: undefined,
    prefix: undefined,
    firstName: undefined,
    lastName: undefined,
    nickname: undefined,
    dateOfBirth: undefined,
    age: undefined,
    gender: undefined,
    policeRegion: undefined,
    provincial: undefined,
    station: undefined,
    fatherName: undefined,
    fatherIdcard: undefined,
    motherName: undefined,
    motherIdcard: undefined,
    maritalStatus: undefined,
    deathStatus: undefined,
    dateOfDeath: undefined,
})

const open = ref(false)

const personTypeOptions = ref<{ label: string; value: any }[]>([])
const generalPersonType32Options = ref<{ label: string; value: any }[]>([])
const crimePersonType23Options = ref<{ label: string; value: any }[]>([])
const typeNotiOptions = ref<{ label: string; value: any }[]>([])
const idTypeOptions = [
    { label: 'บัตรประจำประชาชน', value: 'idCard' },
    { label: 'ทะเบียนบ้าน/หนังสือเดินทางอื่นๆ', value: 'passport' }
]
const genderOptions = ref<{ label: string; value: any }[]>([])
const prefixOptions = ref<{ label: string; value: any }[]>([])

const policeRegionOption = ref<{ label: string; value: any }[]>([])
const policeProvincialOptions = ref<{ label: string; value: any }[]>([])
const policeStationOptions = ref<{ label: string; value: any }[]>([])

const maritalStatusOptions = ref<{ label: string; value: any }[]>([])
const deathStatusOptions = ref<{ label: string; value: any }[]>([])

const toast = useToast()

const fetchOption = async () => {
    try {
        const resp = await useApi<{
            detail: {
                kwatch:
                {
                    type: { id: number; title: string }[],
                    personType: { id: number; title: string }[],
                    crimesType: { id: number; title: string }[],
                    sex: { id: number; title: string }[],
                    titlename: { id: number; title: string }[],
                    policeRegion: { id: number; title: string }[],
                    maritalStatus: { id: number; title: string }[],
                    deathStatus: { id: number; title: string }[],
                    typeNoti: { id: string; title: string }[],
                }
            }
        }>('/api/v1/options')

        console.log('resp.details.listOptions.kwatch', resp.detail);

        const apiPersonTypeOptions = resp.detail.kwatch.type.map(item => ({
            label: item.title,
            value: item.id.toString(),
        }))
        personTypeOptions.value = [...apiPersonTypeOptions]

        const apiGeneralPersonType32Options = resp.detail.kwatch.personType.map(item => ({
            label: item.title,
            value: item.id.toString(),
        }))
        generalPersonType32Options.value = [...apiGeneralPersonType32Options]

        const apiCrimePersonType23Options = resp.detail.kwatch.crimesType.map(item => ({
            label: item.title,
            value: item.id.toString(),
        }))
        crimePersonType23Options.value = [...apiCrimePersonType23Options]

        const apiTypeNotiOptions = resp.detail.kwatch.typeNoti.map(item => ({
            label: item.title,
            value: item.id.toString(),
        }))
        typeNotiOptions.value = [...apiTypeNotiOptions]

        const apiGenderOptions = resp.detail.kwatch.sex.map(item => ({
            label: item.title,
            value: item.id.toString(),
        }))
        genderOptions.value = [...apiGenderOptions]

        const apiPrefixOptions = resp.detail.kwatch.titlename.map(item => ({
            label: item.title,
            value: item.id.toString(),
        }))
        prefixOptions.value = [...apiPrefixOptions]

        const apiPoliceRegion = resp.detail.kwatch.policeRegion.map(item => ({
            label: item.title,
            value: item.id.toString(),
        }))
        policeRegionOption.value = [...apiPoliceRegion]

        const apiMaritalStatusOptions = resp.detail.kwatch.maritalStatus.map(item => ({
            label: item.title,
            value: item.id.toString(),
        }))
        maritalStatusOptions.value = [...apiMaritalStatusOptions]

        const apiDeathStatusOptions = resp.detail.kwatch.deathStatus.map(item => ({
            label: item.title,
            value: item.id.toString(),
        }))
        deathStatusOptions.value = [...apiDeathStatusOptions]

    } catch (err) {
        console.error('Error fetching users:', err)
    }
}
onMounted(fetchOption)
async function onSubmit(event: FormSubmitEvent<Schema>) {
    console.log('event', event);

    if (
        !state.motherIdcard || !isValidThaiId(state.motherIdcard) ||
        !state.fatherIdcard || !isValidThaiId(state.fatherIdcard)
    ) {
        alert('เลขบัตรประชาชนไม่ถูกต้อง');
        return;
    }

    console.log('Form Data:', event.data);

    const formData = new FormData();

    // แนบข้อมูลจาก event.data
    formData.append('type', event.data.personType || '');
    formData.append('personalType', event.data.generalPersonType32 || '');
    formData.append('crimesType', event.data.crimePersonType23 || '');
    formData.append('typeNoti', (event.data.typeNoti && event.data.typeNoti[0]) || '');
    formData.append('idcard', event.data.idType == 'idCard' ? event.data.idNumber || '' : '');
    formData.append('passport', event.data.idType == 'passport' ? event.data.passport || '' : '');
    formData.append('titlename', event.data.prefix || '');
    formData.append('subTitlename', '');
    formData.append('firstname', event.data.firstName || '');
    formData.append('lastname', event.data.lastName || '');
    formData.append('nickname', event.data.nickname || '');
    formData.append('sex', event.data.gender || '');
    formData.append('birthday', event.data.dateOfBirth || '');
    formData.append('age', event.data.age?.toString() || '');
    formData.append('fatherName', event.data.fatherName || '');
    formData.append('fatherIdcard', event.data.fatherIdcard || '');
    formData.append('motherName', event.data.motherName || '');
    formData.append('motherIdcard', event.data.motherIdcard || '');
    formData.append('maritalStatus', event.data.maritalStatus || '');
    formData.append('deathStatus', event.data.deathStatus || '');
    formData.append('dateOfDeath', event.data.dateOfDeath || '');
    formData.append('policeRegion', event.data.policeRegion || '');
    formData.append('policeProvincial', event.data.provincial || '');
    formData.append('policeStation', event.data.station || '');

    // แนบไฟล์รูปภาพ
    if (selectedFile.value) {
        formData.append('photo', selectedFile.value);
    }

    // ผู้บันทึก
    if (authStore.user) {
        formData.append('userRecorder', `${authStore.user.firstName} ${authStore.user.lastName}`);
        formData.append('userPosition', authStore.user.role);
    } else {
        formData.append('userRecorder', `ทดสอบ ทดสอบ`);
        formData.append('userPosition', 'ทดสอบ');
    }

    // Debug log formData
    for (const [key, val] of formData.entries()) {
        console.log(`${key}:`, val);
    }

    try {
        const response = await useApi('/api/v1/kwatch/watchlist', {
            method: 'POST',
            body: formData,
            headers: {}
        });

        const result = response;
        console.log('ผลลัพธ์:', result);

        if (!result.status) {
            if (result.code === 'BAD_PHOTO_NO_FACE') {
                alert('รูปภาพไม่พบใบหน้า กรุณาอัปโหลดใหม่');
            } else {
                alert(result.message || 'เกิดข้อผิดพลาด');
            }
            return;
        }

        open.value = false;
        toast.add({
            title: 'Success',
            description: `New WatchMan ${event.data.firstName} ${event.data.lastName} added`,
            color: 'success'
        });
        router.push({ path: '/police', query: { reload: Date.now().toString() } })
    } catch (err) {
        console.error('Upload failed:', err);
        alert('เกิดข้อผิดพลาดในการอัปโหลดไฟล์');
    }

}
// โหลด provincial ตาม regionId
const fetchProvincial = async (regionId: string) => {
    try {
        const resp = await useApiWatchMan<{ listPoliceProvincial: { id: number; title: string }[] }>(
            `/WatchmanData/api_options.php?f=provincial&regionId=${regionId}`
        )
        policeProvincialOptions.value = resp.listPoliceProvincial.map(item => ({
            label: item.title,
            value: item.id.toString(),
        }))
    } catch (err) {
        console.error('Error fetching provincial:', err)
    }
}

// โหลด station ตาม provincialId
const fetchStation = async (provincialId: string) => {
    try {
        const resp = await useApiWatchMan<{ listPoliceStation: { id: number; title: string }[] }>(
            `/WatchmanData/api_options.php?f=station&provincialId=${provincialId}`
        )
        policeStationOptions.value = resp.listPoliceStation.map(item => ({
            label: item.title,
            value: item.id.toString(),
        }))
    } catch (err) {
        console.error('Error fetching station:', err)
    }
}
watch(() => state.policeRegion, (regionId) => {
    if (regionId) {
        state.provincial = undefined
        state.station = undefined
        fetchProvincial(regionId)
    }
})

watch(() => state.provincial, (provincialId) => {
    if (provincialId) {
        state.station = undefined
        fetchStation(provincialId)
    }
})

const fileInput = ref<HTMLInputElement | null>(null)
const selectedFile = ref<File | null>(null)
const imagePreviewUrl = ref<string | null>(null)

const triggerFileInput = () => {
    fileInput.value?.click()
}

const handleFileChange = (event: Event) => {
    const target = event.target as HTMLInputElement
    const file = target.files?.[0]

    if (file) {
        selectedFile.value = file
        imagePreviewUrl.value = URL.createObjectURL(file)
    }
}

function isValidThaiId(id?: string): boolean {
    if (!id || !/^\d{13}$/.test(id)) return false;

    let sum = 0;
    for (let i = 0; i < 12; i++) {
        sum += parseInt(id[i]) * (13 - i);
    }

    const checkDigit = (11 - (sum % 11)) % 10;
    return checkDigit === parseInt(id[12]);
}

watch(() => state.dateOfBirth, (newDate) => {
    if (!newDate) {
        state.age = null
        return
    }

    const birthDate = new Date(newDate)
    const today = new Date()

    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    const dayDiff = today.getDate() - birthDate.getDate()

    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
        age--
    }

    state.age = age
})

const onlyNumber = (event: KeyboardEvent) => {
    const charCode = event.which ? event.which : event.keyCode
    if (charCode < 48 || charCode > 57) {
        event.preventDefault()
    }
}

</script>

<template>
    <UDashboardPanel id="users">
        <template #header>
            <UDashboardNavbar :title="t('systemUsers.title')">
                <template #leading>
                    <UDashboardSidebarCollapse />
                </template>
            </UDashboardNavbar>
        </template>

        <template #body>
            <UForm :schema="schema" :state="state" class="space-y-6 p-4" @submit="onSubmit">
                <div class="border p-4 rounded-lg shadow-sm" style="display: flex;justify-content: space-around;">
                    <div style="flex: 1; max-width: 50%; margin-right: 20px;">
                        <h3 class="text-lg font-semibold mb-4">กรอกข้อมูลบุคคล</h3>
                        <p class="text-sm text-gray-500 mb-4">กรุณากรอกข้อมูลให้ครบถ้วนเพื่อความสะดวกในการติดต่อ</p>

                        <div class="flex flex-col space-y-4">
                            <UFormField label="ลำดับ ประเภทบุคคล" name="personType">
                                <URadioGroup v-model="state.personType" :items="personTypeOptions"
                                    orientation="horizontal" />
                            </UFormField>

                            <!-- แสดงถ้าเลือก บุคคลทั่วไป -->
                            <UFormField v-if="state.personType == '1'" label="บุคคลทั่วไป 32 ประเภท"
                                name="generalPersonType32">
                                <USelect v-model="state.generalPersonType32" :items="generalPersonType32Options"
                                    placeholder="เลือก" class="w-full" />
                            </UFormField>

                            <!-- แสดงถ้าเลือก บุคคลเกี่ยวข้องอาชญากรรม -->
                            <UFormField v-if="state.personType !== '1'" label="บุคคลอาชญากรรม 23 ประเภท"
                                name="crimePersonType23">
                                <USelect v-model="state.crimePersonType23" :items="crimePersonType23Options"
                                    placeholder="เลือก" class="w-full" />
                            </UFormField>
                            <UFormField v-if="state.personType !== '1'" label="บุคคลอาชญากรรม 23 ประเภท"
                                name="crimePersonType23">
                                <div class="space-y-2">
                                    <div v-for="option in typeNotiOptions" :key="option.value"
                                        class="flex items-center space-x-2">
                                        <input type="checkbox" :id="option.value" :value="option.value"
                                            v-model="state.typeNoti"
                                            :disabled="(state.typeNoti?.length ?? 0) >= 2 && !state.typeNoti?.includes(option.value)"
                                            class="rounded border-gray-300" />

                                        <label :for="option.value">{{ option.label }}</label>
                                    </div>
                                </div>
                            </UFormField>

                            <!-- Image Upload Area -->
                            <UFormField label="รูปภาพหน้าของบุคคล" name="personImage">
                                <!-- ปุ่มเลือกไฟล์ -->
                                <UButton icon="i-lucide-plus" label="เพิ่มรูปภาพ" variant="outline"
                                    @click="triggerFileInput" />
                                <div
                                    class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center flex flex-col items-center justify-center h-60 space-y-2 relative">
                                    <!-- input ซ่อน -->
                                    <input ref="fileInput" type="file" accept="image/*" class="hidden"
                                        @change="handleFileChange" />

                                    <!-- แสดงรูปภาพที่เลือก -->
                                    <img v-if="imagePreviewUrl" :src="imagePreviewUrl" alt="ภาพที่เลือก"
                                        class="absolute inset-0 w-full h-full object-contain rounded-md" />
                                </div>
                            </UFormField>

                            <UFormField name="idType">
                                <URadioGroup v-model="state.idType" :items="idTypeOptions" orientation="horizontal" />
                            </UFormField>

                            <!-- แสดงเฉพาะเมื่อเลือก idCard -->
                            <UFormField v-if="state.idType === 'idCard'" label="เลขบัตรประชาชน"
                                placeholder="กรอกเลขบัตรประจำประชาชน 13 หลัก เฉพาะตัวเลขเท่านั้น" name="idNumber">
                                <UInput v-model="state.idNumber" type="text" maxlength="13" inputmode="numeric"
                                    pattern="[0-9]*" class="w-full" @keypress="onlyNumber($event)" />
                            </UFormField>


                            <!-- แสดงเฉพาะเมื่อเลือก passport -->
                            <UFormField v-else label="เลขทะเบียนบ้าน/หนังสือเดินทางอื่นๆ"
                                placeholder="กรอกเลขทะเบียนบ้าน/หนังสือเดินทางอื่นๆ" name="passport">
                                <UInput v-model="state.passport" type="text" maxlength="50" class="w-full" />
                            </UFormField>

                            <div class="grid grid-cols-2 gap-4">
                                <UFormField label="เพศ" name="gender">
                                    <USelect v-model="state.gender" :items="genderOptions" placeholder="เลือก"
                                        class="w-full" />
                                </UFormField>
                                <UFormField label="คำนำหน้า" name="prefix">
                                    <USelect v-model="state.prefix" :items="prefixOptions" placeholder="เลือก"
                                        class="w-full" />
                                </UFormField>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <UFormField label="ชื่อ" placeholder="ชื่อ" name="firstName">
                                    <UInput v-model="state.firstName" class="w-full" />
                                </UFormField>
                                <UFormField label="นามสกุล" placeholder="นามสกุล" name="lastName">
                                    <UInput v-model="state.lastName" class="w-full" />
                                </UFormField>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <UFormField label="ชื่อเล่น/ฉายา" placeholder="ชื่อเล่น/ฉายา" name="nickname">
                                    <UInput v-model="state.nickname" class="w-full" />
                                </UFormField>
                                <UFormField label="วันเดือนปีเกิด" name="dateOfBirth">
                                    <UInput v-model="state.dateOfBirth" type="date" class="w-full" />
                                </UFormField>
                            </div>

                            <UFormField label="อายุ" name="age">
                                <UInput v-model.number="state.age" type="number" class="w-full" />
                            </UFormField>
                        </div>
                    </div>

                    <div style="flex: 1; max-width: 50%;">

                        <!-- <UFormField label="บช." name="policeRegion">
              <USelect v-model="state.policeRegion" :items="policeRegionOption" placeholder="เลือก" class="w-full" />
            </UFormField> -->
                        <!-- Right Column -->
                        <div class="flex flex-col space-y-4">
                            <div class="grid grid-cols-2 gap-4">
                                <UFormField label="ชื่อ - นามสกุล บิดา" name="fatherName">
                                    <UInput v-model="state.fatherName" class="w-full" />
                                </UFormField>
                                <UFormField label="เลขที่บัตรประชาชน บิดา" name="fatherIdcard">
                                    <UInput v-model="state.fatherIdcard" type="text" inputmode="numeric"
                                        pattern="[0-9]*" maxlength="13" class="w-full" />
                                </UFormField>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <UFormField label="ชื่อ - นามสกุล มารดา" name="motherName">
                                    <UInput v-model="state.motherName" class="w-full" />
                                </UFormField>
                                <UFormField label="เลขที่บัตรประชาชน มารดา" name="motherIdcard">
                                    <UInput v-model="state.motherIdcard" type="text" inputmode="numeric"
                                        pattern="[0-9]*" maxlength="13" class="w-full" />
                                </UFormField>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <UFormField label="สถานภาพสมรส" name="maritalStatus">
                                    <USelect v-model="state.maritalStatus" :items="maritalStatusOptions"
                                        placeholder="เลือก" class="w-full" />
                                </UFormField>

                                <UFormField label="สถานะการมีชีวิตอยู่" name="deathStatus">
                                    <URadioGroup v-model="state.deathStatus" :items="deathStatusOptions"
                                        orientation="horizontal" />
                                </UFormField>

                                <!-- แสดงเฉพาะเมื่อเลือก "เสียชีวิต" -->
                                <UFormField v-if="Number(state.deathStatus) === 2" label="วันเดือนปีที่เสียชีวิต"
                                    name="dateOfDeath">
                                    <UInput v-model="state.dateOfDeath" type="date" class="w-full" />
                                </UFormField>
                            </div>

                            <!-- Section: สถานที่ติดต่อ (Contact Information) -->
                            <div class="p-4 rounded-lg">
                                <h3 class="text-lg font-semibold mb-4">สถานที่ติดต่อ (รับผิดชอบสถานที่ ที่พักอาศัย)</h3>
                                <p class="text-sm text-gray-500 mb-4">กรุณากรอกข้อมูลให้ครบถ้วนเพื่อความสะดวกในการติดต่อ
                                </p>

                                <UFormField label="บช." name="policeRegion">
                                    <USelect v-model="state.policeRegion" :items="policeRegionOption"
                                        placeholder="เลือก" class="w-full" />
                                </UFormField>
                                <UFormField label="บก." name="provincial">
                                    <USelect v-model="state.provincial" :items="policeProvincialOptions"
                                        placeholder="เลือก" class="w-full" />
                                </UFormField>
                                <UFormField label="สป./สก." name="station">
                                    <USelect v-model="state.station" :items="policeStationOptions" placeholder="เลือก"
                                        class="w-full" />
                                </UFormField>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Action Buttons -->
                <div class="flex justify-end gap-2 mt-6">
                    <UButton label="Cancel" color="neutral" variant="subtle" @click="open = false" />
                    <UButton label="บันทึกข้อมูล" color="primary" variant="solid" type="submit" icon="i-lucide-save" />
                </div>
            </UForm>
        </template>
    </UDashboardPanel>
</template>

<style scoped>
/* Add any specific styles here if needed, though Tailwind should handle most */
</style>
