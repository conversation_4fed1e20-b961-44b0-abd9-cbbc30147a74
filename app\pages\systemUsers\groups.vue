<script setup lang="ts">
const links = [
  {
    label: 'กลุ่มย่อย',
    to: '/organizations-groups',
    exact: true
  },
  {
    label: 'สมาชิก',
    to: '/organizations-groups/members'
  }
]
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UBreadcrumb :items="links">
        <template #separator>
          <span class="mx-2 text-muted">/</span>
        </template>
      </UBreadcrumb>

      <UDashboardToolbar class="py-0 px-1.5 overflow-x-auto">
        <UHorizontalNavigation :links="links" />
      </UDashboardToolbar>

      <NuxtPage />
    </UDashboardPanel>
  </UDashboardPage>
</template>
