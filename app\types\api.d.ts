// app/types/api.d.ts
export interface KControlNotification {
  id: string
  name: string
  district: string
  lat: number
  lng: number
  hwId: string
  message: string
  status: string
  datetime: string
}

export interface KControl {
  id: string
  name: string
  district: string
  lat: number
  long: number
  brand: string
  state: string
  hwId: string
  approved: boolean
  healthInterval: number
  alarmInterval: number
  status: string
  alarm: boolean
  lastSeen: string
  stats: {
    OnlineCount: number
    WarningCount: number
    OfflineCount: number
    AlarmCount: number
  }
  dateTimeCreate: string
  dateTimeUpdate: string
}

export interface Devices {
  id: string
  name: string
  user: string
  password: string
  url: string
  district: string
  lat: number
  long: number
  brand: string
  status: boolean
  state: string
  dateTimeCreate: string
  dateTimeUpdate: string
}
