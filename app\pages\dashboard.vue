<template>
  <UDashboardPanel id="watchman">
    <template #header>
      <UDashboardNavbar :title="'dashboard'">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <div class="date-time">
            <svg width="27" height="26" viewBox="0 0 27 26" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M25.9943 22.9648C25.1639 22.2246 24.4369 21.3759 23.8329 20.4418C23.1729 19.1525 22.7776 17.7442 22.6704 16.2998V12.0459C22.6761 9.77742 21.8532 7.58492 20.3564 5.88035C18.8595 4.17578 16.7917 3.07647 14.5415 2.78898V1.67815C14.5415 1.37326 14.4204 1.08086 14.2048 0.86527C13.9892 0.649681 13.6968 0.528564 13.3919 0.528564C13.0871 0.528564 12.7947 0.649681 12.5791 0.86527C12.3635 1.08086 12.2424 1.37326 12.2424 1.67815V2.8062C10.0123 3.11442 7.96951 4.22037 6.49232 5.91921C5.01513 7.61806 4.20366 9.79467 4.2082 12.0459V16.2998C4.10104 17.7442 3.70577 19.1525 3.0457 20.4418C2.45229 21.3738 1.73698 22.2223 0.918754 22.9648C0.826901 23.0455 0.753284 23.1448 0.702803 23.2562C0.652321 23.3676 0.626131 23.4884 0.625977 23.6106V24.7818C0.625977 25.0101 0.7167 25.2292 0.87819 25.3907C1.03968 25.5521 1.25871 25.6429 1.48709 25.6429H25.426C25.6544 25.6429 25.8734 25.5521 26.0349 25.3907C26.1964 25.2292 26.2871 25.0101 26.2871 24.7818V23.6106C26.2869 23.4884 26.2607 23.3676 26.2103 23.2562C26.1598 23.1448 26.0862 23.0455 25.9943 22.9648ZM2.41709 23.9206C3.21809 23.1465 3.92348 22.2792 4.5182 21.3373C5.34985 19.7804 5.83456 18.0619 5.93903 16.2998V12.0459C5.90488 11.0367 6.07416 10.031 6.43679 9.08858C6.79942 8.14618 7.34799 7.28638 8.04983 6.56039C8.75166 5.8344 9.59242 5.25707 10.522 4.86277C11.4516 4.46848 12.4511 4.26528 13.4608 4.26528C14.4706 4.26528 15.4701 4.46848 16.3997 4.86277C17.3293 5.25707 18.17 5.8344 18.8718 6.56039C19.5737 7.28638 20.1222 8.14618 20.4849 9.08858C20.8475 10.031 21.0168 11.0367 20.9826 12.0459V16.2998C21.0871 18.0619 21.5718 19.7804 22.4035 21.3373C22.9982 22.2792 23.7036 23.1465 24.5046 23.9206H2.41709Z"
                fill="#9B0000" />
            </svg>
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M26.1998 5C23.4998 2.5 19.9498 1 15.9998 1C12.0498 1 8.4998 2.5 5.7998 5H26.1998ZM5.7998 27C8.4998 29.5 12.0498 31 15.9998 31C19.9498 31 23.4998 29.5 26.1998 27H5.7998Z"
                fill="#ED4C5C" />
              <path
                d="M1 16C1 18.15 1.45 20.15 2.25 22H29.75C30.55 20.15 31 18.15 31 16C31 13.85 30.55 11.85 29.75 10H2.25C1.45 11.85 1 13.85 1 16Z"
                fill="#2A5F9E" />
              <path
                d="M5.8002 27H26.1502C27.6502 25.6 28.9002 23.9 29.7002 22H2.2002C3.1002 23.9 4.3002 25.6 5.8002 27ZM26.2002 5H5.8002C4.3002 6.4 3.0502 8.1 2.2502 10H29.7502C28.9002 8.1 27.7002 6.4 26.2002 5Z"
                fill="#F9F9F9" />
            </svg>

          </div>
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="dashboard-layout">
        <div class="mt-1">
          <Filter />
        </div>
        <div class="main-content">
          <!-- Sidebar ซ้าย -->
          <div>
            <!-- ปุ่ม Tab -->
            <div class="grid grid-cols-2 gap-1 mb-2">
              <!-- ปุ่ม b1 -->
              <div class="col-span-1 bg-gray-200 p-1 text-center text-[16px]">
                <button @click="selectedTab = 'b1'"
                  :style="selectedTab === 'b1' ? { backgroundColor: '#9B0000', color: '#fff' } : {}"
                  class="w-full py-2 rounded flex justify-center items-center">
                  <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M4.5 6C4.5 5.60218 4.65804 5.22064 4.93934 4.93934C5.22064 4.65804 5.60218 4.5 6 4.5H30C30.3978 4.5 30.7794 4.65804 31.0607 4.93934C31.342 5.22064 31.5 5.60218 31.5 6V9C31.5 9.39782 31.342 9.77936 31.0607 10.0607C30.7794 10.342 30.3978 10.5 30 10.5H6C5.60218 10.5 5.22064 10.342 4.93934 10.0607C4.65804 9.77936 4.5 9.39782 4.5 9V6ZM12 21C12 22.5913 12.6321 24.1174 13.7574 25.2426C14.8826 26.3679 16.4087 27 18 27C19.5913 27 21.1174 26.3679 22.2426 25.2426C23.3679 24.1174 24 22.5913 24 21C24 19.4087 23.3679 17.8826 22.2426 16.7574C21.1174 15.6321 19.5913 15 18 15C16.4087 15 14.8826 15.6321 13.7574 16.7574C12.6321 17.8826 12 19.4087 12 21Z"
                      :stroke="selectedTab === 'b1' ? '#ffffff' : '#000000'" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round" />
                    <path
                      d="M28.5 10.5V21C28.5 23.7848 27.3938 26.4555 25.4246 28.4246C23.4555 30.3938 20.7848 31.5 18 31.5C15.2152 31.5 12.5445 30.3938 10.5754 28.4246C8.60625 26.4555 7.5 23.7848 7.5 21V10.5M18 21H18.015"
                      :stroke="selectedTab === 'b1' ? '#ffffff' : '#000000'" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round" />
                  </svg>
                </button>
              </div>

              <!-- ปุ่ม b2 -->
              <div class="col-span-1 bg-gray-200 p-1 text-center text-[16px]">
                <button @click="selectedTab = 'b2'"
                  :style="selectedTab === 'b2' ? { backgroundColor: '#9B0000', color: '#fff' } : {}"
                  class="w-full py-2 rounded flex justify-center items-center">

                  <svg width="38" height="38" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M26.5207 19.0001H32.2713C32.5411 19.0002 32.8064 19.0693 33.042 19.2007C33.2776 19.3322 33.4757 19.5217 33.6175 19.7512C33.7593 19.9807 33.8401 20.2426 33.8522 20.5121C33.8644 20.7817 33.8074 21.0498 33.6868 21.2911L30.4663 27.7337C30.3442 27.9778 30.1611 28.1863 29.9347 28.3387C29.7082 28.4912 29.4462 28.5825 29.1741 28.6039C28.902 28.6252 28.629 28.5759 28.3815 28.4606C28.1341 28.3453 27.9207 28.168 27.762 27.9459L24.399 23.2434M3.1665 30.0834H9.11984C9.70999 30.0875 10.2896 29.9266 10.7931 29.6189C11.2967 29.3111 11.7043 28.8688 11.9698 28.3417L14.2498 23.7501M3.1665 33.2501V26.9167M11.0832 14.2501H11.099M27.0843 14.334C27.4596 14.5218 27.7449 14.851 27.8777 15.2491C28.0104 15.6472 27.9796 16.0818 27.7921 16.4572L22.8743 26.2913C22.7812 26.4773 22.6525 26.6432 22.4953 26.7794C22.3382 26.9157 22.1558 27.0196 21.9585 27.0854C21.7611 27.1511 21.5528 27.1773 21.3454 27.1625C21.1379 27.1477 20.9354 27.0921 20.7494 26.9991L5.71567 19.4751C4.6237 18.925 3.79406 17.9648 3.40831 16.8046C3.02256 15.6444 3.1121 14.3786 3.65734 13.2842L5.84234 8.86672C6.11467 8.32398 6.49125 7.84021 6.95057 7.44304C7.40989 7.04586 7.94295 6.74305 8.51931 6.55192C9.09567 6.36078 9.70404 6.28505 10.3097 6.32905C10.9153 6.37306 11.5063 6.53593 12.049 6.80838L27.0843 14.334Z"
                      :stroke="selectedTab === 'b2' ? '#ffffff' : '#000000'" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round" />
                  </svg>

                </button>
              </div>
            </div>

            <!-- เนื้อหา Tab -->
            <div>
              <LeftSidebar1 v-if="selectedTab === 'b1'" />
              <LeftSidebar2 v-else-if="selectedTab === 'b2'" />
            </div>
          </div>
          <!-- Center -->
          <div class="overview-section">
            <!-- Row 1 -->
            <div class="grid grid-cols-8 gap-1 mb-1 text-[16px] text-center">
              <div class="col-span-3 bg-[#9B0000] text-white p-1">
                จำนวนกล้องที่เชื่อมต่อทั้งหมดในระบบ
              </div>
              <div class="col-span-1 bg-gray-200 text-[#9B0000] p-1">
                จำนวนที่ตรวจจับได้
              </div>
              <div class="col-span-1 bg-gray-200 text-[#9B0000] p-1">
                ใบหน้า
              </div>
              <div class="col-span-1 bg-gray-200 text-[#9B0000] p-1">
                ป้ายทะเบียน
              </div>
              <div class="col-span-1 bg-gray-200 text-[#9B0000] p-1">
                วัตถุ
              </div>
              <div class="col-span-1 bg-gray-200 text-[#9B0000] p-1">
                พฤติกรรม
              </div>
            </div>


            <!-- Row 2 -->
            <div class="grid grid-cols-8 gap-1 mb-1">
              <div class="flex items-center gap-2 bg-gray-200 p-1">
                <span>
                  <svg width="25" height="25" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M4.5 6C4.5 5.60218 4.65804 5.22064 4.93934 4.93934C5.22064 4.65804 5.60218 4.5 6 4.5H30C30.3978 4.5 30.7794 4.65804 31.0607 4.93934C31.342 5.22064 31.5 5.60218 31.5 6V9C31.5 9.39782 31.342 9.77936 31.0607 10.0607C30.7794 10.342 30.3978 10.5 30 10.5H6C5.60218 10.5 5.22064 10.342 4.93934 10.0607C4.65804 9.77936 4.5 9.39782 4.5 9V6ZM12 21C12 22.5913 12.6321 24.1174 13.7574 25.2426C14.8826 26.3679 16.4087 27 18 27C19.5913 27 21.1174 26.3679 22.2426 25.2426C23.3679 24.1174 24 22.5913 24 21C24 19.4087 23.3679 17.8826 22.2426 16.7574C21.1174 15.6321 19.5913 15 18 15C16.4087 15 14.8826 15.6321 13.7574 16.7574C12.6321 17.8826 12 19.4087 12 21Z"
                      stroke="#9B0000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M28.5 10.5V21C28.5 23.7848 27.3938 26.4555 25.4246 28.4246C23.4555 30.3938 20.7848 31.5 18 31.5C15.2152 31.5 12.5445 30.3938 10.5754 28.4246C8.60625 26.4555 7.5 23.7848 7.5 21V10.5M18 21H18.015"
                      stroke="#9B0000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                </span>
                <div class="flex flex-col text-sm leading-tight w-full">
                  <span class="text-[20px] font-semibold">120</span>
                  <span class="text-end text-[16px] text-gray-700">กล้อง</span>
                </div>
              </div>
              <div class="flex items-center gap-2 bg-gray-200 p-1">
                <span>
                  <svg width="25" height="25" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#filter0_d_116_12385)">
                      <circle cx="16" cy="12" r="12" fill="#017A43" />
                    </g>
                    <defs>
                      <filter id="filter0_d_116_12385" x="0" y="0" width="32" height="32" filterUnits="userSpaceOnUse"
                        color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix" />
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                          result="hardAlpha" />
                        <feOffset dy="4" />
                        <feGaussianBlur stdDeviation="2" />
                        <feComposite in2="hardAlpha" operator="out" />
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_116_12385" />
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_116_12385" result="shape" />
                      </filter>
                    </defs>
                  </svg>
                </span>
                <div class="flex flex-col text-sm leading-tight w-full">
                  <span class="text-[20px] font-semibold">120</span>
                  <span class="text-end text-[16px] text-gray-700">กล้อง</span>
                </div>
              </div>
              <div class="flex items-center gap-2 bg-gray-200 p-1">
                <span>
                  <svg width="25" height="25" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#filter0_d_116_12388)">
                      <circle cx="16" cy="12" r="12" fill="#ED4C5C" />
                    </g>
                    <defs>
                      <filter id="filter0_d_116_12388" x="0" y="0" width="32" height="32" filterUnits="userSpaceOnUse"
                        color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix" />
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                          result="hardAlpha" />
                        <feOffset dy="4" />
                        <feGaussianBlur stdDeviation="2" />
                        <feComposite in2="hardAlpha" operator="out" />
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_116_12388" />
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_116_12388" result="shape" />
                      </filter>
                    </defs>
                  </svg>
                </span>
                <div class="flex flex-col text-sm leading-tight w-full">
                  <span class="text-[20px] font-semibold">120</span>
                  <span class="text-end text-[16px] text-gray-700">กล้อง</span>
                </div>
              </div>
              <div class="flex items-center gap-2 bg-gray-200 p-1">
                <span>
                  <svg width="25" height="25" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M19.1241 0C16.8696 0 14.7074 0.842866 13.1133 2.34318C11.5191 3.84349 10.6235 5.87834 10.6235 8.0001C10.6235 10.1219 11.5191 12.1567 13.1133 13.657C14.7074 15.1573 16.8696 16.0002 19.1241 16.0002C21.3786 16.0002 23.5407 15.1573 25.1349 13.657C26.729 12.1567 27.6246 10.1219 27.6246 8.0001C27.6246 5.87834 26.729 3.84349 25.1349 2.34318C23.5407 0.842866 21.3786 0 19.1241 0ZM12.7487 8.0001C12.7487 6.40878 13.4204 4.88264 14.616 3.75741C15.8116 2.63218 17.4332 2.00003 19.1241 2.00003C20.8149 2.00003 22.4365 2.63218 23.6322 3.75741C24.8278 4.88264 25.4995 6.40878 25.4995 8.0001C25.4995 9.59142 24.8278 11.1176 23.6322 12.2428C22.4365 13.368 20.8149 14.0002 19.1241 14.0002C17.4332 14.0002 15.8116 13.368 14.616 12.2428C13.4204 11.1176 12.7487 9.59142 12.7487 8.0001ZM29.4628 29.5944C27.1868 30.9604 24.2478 31.7224 21.0431 31.9364C20.8831 31.5393 20.6354 31.1787 20.3163 30.8784L19.3812 29.9984C22.9621 29.9664 26.0966 29.2404 28.3195 27.9064C30.5509 26.5663 31.8749 24.6203 31.8749 22.0003C31.8749 21.4698 31.651 20.9611 31.2524 20.5861C30.8539 20.211 30.3134 20.0003 29.7497 20.0003H16.4549C16.1891 19.2944 15.8307 18.6225 15.3881 18.0002H29.7497C30.877 18.0002 31.958 18.4217 32.7551 19.1718C33.5522 19.922 34 20.9394 34 22.0003C34 25.3823 32.2298 27.9324 29.4628 29.5944ZM7.43584 30.0004C9.10619 30.0004 10.649 29.4804 11.8901 28.6064L17.3092 33.7064C17.4079 33.7994 17.525 33.8732 17.654 33.9236C17.7829 33.9739 17.9211 33.9999 18.0608 34C18.2004 34.0001 18.3386 33.9743 18.4677 33.9241C18.5967 33.8739 18.7139 33.8003 18.8127 33.7074C18.9115 33.6146 18.9899 33.5043 19.0434 33.383C19.097 33.2616 19.1246 33.1315 19.1247 33.0001C19.1248 32.8687 19.0974 32.7386 19.044 32.6172C18.9907 32.4958 18.9125 32.3854 18.8138 32.2924L13.3947 27.1924C14.3978 25.9299 14.9187 24.3857 14.8732 22.8099C14.8276 21.2341 14.2183 19.7189 13.1437 18.5096C12.0692 17.3004 10.5924 16.4678 8.95249 16.1469C7.31258 15.8259 5.60556 16.0352 4.10782 16.741C2.61008 17.4468 1.40931 18.6077 0.699951 20.0358C-0.00941338 21.4638 -0.185846 23.0754 0.199222 24.6096C0.584289 26.1437 1.50831 27.5107 2.82167 28.489C4.13503 29.4674 5.76084 29.9999 7.43584 30.0004ZM7.43584 28.0004C6.02679 28.0004 4.67545 27.4736 3.6791 26.5359C2.68275 25.5982 2.123 24.3264 2.123 23.0003C2.123 21.6742 2.68275 20.4024 3.6791 19.4647C4.67545 18.527 6.02679 18.0002 7.43584 18.0002C8.84489 18.0002 10.1962 18.527 11.1926 19.4647C12.1889 20.4024 12.7487 21.6742 12.7487 23.0003C12.7487 24.3264 12.1889 25.5982 11.1926 26.5359C10.1962 27.4736 8.84489 28.0004 7.43584 28.0004Z"
                      fill="#9B0000" />
                  </svg>
                </span>
                <div class="flex flex-col text-sm leading-tight w-full">
                  <span class="text-[20px] font-semibold">120</span>
                  <span class="text-end text-[16px] text-gray-700">รายการ</span>
                </div>
              </div>
              <div class="flex items-center gap-2 bg-gray-200 p-1">
                <span>
                  <svg width="25" height="25" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M4.16699 13.6451C4.34033 10.1451 4.85866 7.96175 6.41199 6.41175C7.96199 4.85841 10.1453 4.34008 13.6453 4.16675M35.8337 13.6451C35.6603 10.1451 35.142 7.96175 33.5887 6.41175C32.0387 4.85841 29.8553 4.34008 26.3553 4.16675M26.3553 35.8334C29.8553 35.6601 32.0387 35.1418 33.5887 33.5884C35.142 32.0384 35.6603 29.8551 35.8337 26.3551M13.6453 35.8334C10.1453 35.6601 7.96199 35.1418 6.41199 33.5884C4.85866 32.0384 4.34033 29.8551 4.16699 26.3551M29.167 28.3334L28.8303 26.9184C28.6934 26.3442 28.4065 25.8167 27.9989 25.3898C27.5912 24.9628 27.0776 24.6518 26.5103 24.4884L22.5003 23.3317V20.8867C23.9937 19.8784 25.0003 17.9934 25.0003 15.8334C25.0003 12.6117 22.7603 10.0001 20.0003 10.0001C17.2387 10.0001 15.0003 12.6117 15.0003 15.8334C15.0003 17.9934 16.0053 19.8784 17.5003 20.8867V23.3317L13.5153 24.4984C12.9651 24.6595 12.4657 24.9598 12.0654 25.3703C11.6651 25.7808 11.3775 26.2876 11.2303 26.8417L10.8337 28.3334"
                      stroke="#9B0000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                </span>
                <div class="flex flex-col text-sm leading-tight w-full">
                  <span class="text-[20px] font-semibold">120</span>
                  <span class="text-end text-[16px] text-gray-700">รายการ</span>
                </div>
              </div>
              <div class="flex items-center gap-2 bg-gray-200 p-1">
                <span>
                  <svg width="25" height="25" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M33.214 21.0001L29.7514 11.5594C29.5743 11.0778 29.2537 10.662 28.8329 10.3683C28.4121 10.0746 27.9114 9.91696 27.3982 9.91675H12.8149C11.7649 9.91675 10.8245 10.5724 10.4629 11.5594L6.99902 21.0001"
                      stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M15.167 15.75C16.1335 15.75 16.917 14.9665 16.917 14C16.917 13.0335 16.1335 12.25 15.167 12.25C14.2005 12.25 13.417 13.0335 13.417 14C13.417 14.9665 14.2005 15.75 15.167 15.75Z"
                      stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M11.2021 20.9999L11.6886 18.3563C11.7476 18.0339 11.9178 17.7424 12.1696 17.5327C12.4214 17.3229 12.7388 17.2081 13.0665 17.2083H17.2665C17.594 17.208 17.9113 17.3227 18.163 17.5322C18.4148 17.7418 18.5851 18.0329 18.6443 18.3551L19.1308 20.9999"
                      stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M13.4167 21C13.4167 20.5359 13.601 20.0908 13.9292 19.7626C14.2574 19.4344 14.7025 19.25 15.1667 19.25C15.6308 19.25 16.0759 19.4344 16.4041 19.7626C16.7323 20.0908 16.9167 20.5359 16.9167 21M8.16667 31.15V33.8333C8.16667 33.988 8.22812 34.1364 8.33752 34.2458C8.44692 34.3552 8.59529 34.4167 8.75 34.4167H12.8333C12.9099 34.4167 12.9858 34.4016 13.0566 34.3723C13.1273 34.3429 13.1916 34.3 13.2458 34.2458C13.3 34.1916 13.3429 34.1273 13.3723 34.0566C13.4016 33.9858 13.4167 33.9099 13.4167 33.8333V31.15M19.25 31.15H6.41667C5.77033 31.15 5.25 30.6297 5.25 29.9833V22.1667C5.25 21.5203 5.77033 21 6.41667 21H33.8333C34.4797 21 35 21.5203 35 22.1667V26.4833"
                      stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M11.083 27.825C12.0495 27.825 12.833 27.0414 12.833 26.075C12.833 25.1085 12.0495 24.325 11.083 24.325C10.1165 24.325 9.33301 25.1085 9.33301 26.075C9.33301 27.0414 10.1165 27.825 11.083 27.825Z"
                      stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M27.4648 26.4833C27.4329 26.3495 27.4168 26.2125 27.417 26.075C27.417 25.6108 27.6014 25.1657 27.9296 24.8375C28.2577 24.5093 28.7029 24.325 29.167 24.325C29.6311 24.325 30.0762 24.5093 30.4044 24.8375C30.7326 25.1657 30.917 25.6108 30.917 26.075C30.9168 26.2125 30.9003 26.3496 30.868 26.4833"
                      stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M49.5833 26.4834H20.4167C19.7723 26.4834 19.25 27.0057 19.25 27.6501V44.9167C19.25 45.5611 19.7723 46.0834 20.4167 46.0834H49.5833C50.2277 46.0834 50.75 45.5611 50.75 44.9167V27.6501C50.75 27.0057 50.2277 26.4834 49.5833 26.4834Z"
                      stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M22.458 30.1584C22.9413 30.1584 23.333 29.7667 23.333 29.2834C23.333 28.8002 22.9413 28.4084 22.458 28.4084C21.9748 28.4084 21.583 28.8002 21.583 29.2834C21.583 29.7667 21.9748 30.1584 22.458 30.1584Z"
                      fill="#9B0000" />
                    <path
                      d="M24.792 30.1584C25.2752 30.1584 25.667 29.7667 25.667 29.2834C25.667 28.8002 25.2752 28.4084 24.792 28.4084C24.3087 28.4084 23.917 28.8002 23.917 29.2834C23.917 29.7667 24.3087 30.1584 24.792 30.1584Z"
                      fill="#9B0000" />
                    <path
                      d="M28.875 30.1584C29.3582 30.1584 29.75 29.7667 29.75 29.2834C29.75 28.8002 29.3582 28.4084 28.875 28.4084C28.3918 28.4084 28 28.8002 28 29.2834C28 29.7667 28.3918 30.1584 28.875 30.1584Z"
                      fill="#9B0000" />
                    <path
                      d="M31.208 30.1584C31.6913 30.1584 32.083 29.7667 32.083 29.2834C32.083 28.8002 31.6913 28.4084 31.208 28.4084C30.7248 28.4084 30.333 28.8002 30.333 29.2834C30.333 29.7667 30.7248 30.1584 31.208 30.1584Z"
                      fill="#9B0000" />
                    <path
                      d="M26.8337 37.45C27.9612 37.45 28.8753 36.5359 28.8753 35.4084C28.8753 34.2808 27.9612 33.3667 26.8337 33.3667C25.7061 33.3667 24.792 34.2808 24.792 35.4084C24.792 36.5359 25.7061 37.45 26.8337 37.45Z"
                      stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M22.167 43.5751C22.167 42.3374 22.6587 41.1505 23.5338 40.2753C24.409 39.4001 25.596 38.9084 26.8337 38.9084C28.0713 38.9084 29.2583 39.4001 30.1335 40.2753C31.0087 41.1505 31.5003 42.3374 31.5003 43.5751"
                      stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M47.8337 36.5751H34.417V43.5751H47.8337V36.5751ZM47.8337 36.5751V34.2418H37.917V36.5751H47.8337ZM40.2503 34.2418V36.5751M36.7503 38.9084H45.5003M36.7503 41.2418H45.5003M22.167 31.9084H31.5003V43.5751H22.167V31.9084ZM45.5003 29.2834H47.8337V31.6168H45.5003V29.2834Z"
                      stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                </span>
                <div class="flex flex-col text-sm leading-tight w-full">
                  <span class="text-[20px] font-semibold">120</span>
                  <span class="text-end text-[16px] text-gray-700">รายการ</span>
                </div>
              </div>
              <div class="flex items-center gap-2 bg-gray-200 p-1">
                <span>
                  <svg width="25" height="25" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_118_12443)">
                      <path
                        d="M18.3129 16.9695C18.9952 16.6322 19.7084 16.3614 20.4426 16.1609L15.887 3.89506C15.7252 3.45914 15.4339 3.08319 15.0522 2.8177C14.6704 2.55221 14.2166 2.40991 13.7516 2.40991C13.2867 2.40991 12.8328 2.55221 12.4511 2.8177C12.0694 3.08319 11.778 3.45914 11.6162 3.89506L2.27733 28.7684C2.13726 29.1195 2.0868 29.5 2.1305 29.8755C2.1742 30.2511 2.31067 30.6098 2.52762 30.9194C2.74457 31.229 3.03518 31.4798 3.37322 31.649C3.71127 31.8183 4.08612 31.9007 4.46399 31.8889H12.3679C12.0923 31.1522 11.8939 30.3888 11.7757 29.6112H4.46399L13.7345 4.69228L18.3129 16.9695Z"
                        fill="#9B0000" />
                      <path
                        d="M36.444 10.25H25.0551C24.451 10.25 23.8717 10.49 23.4445 10.9171C23.0173 11.3443 22.7773 11.9237 22.7773 12.5278V15.7736H23.5632C24.0612 15.7629 24.5594 15.7819 25.0551 15.8306V12.5278H36.444V23.9167H34.9065C35.1519 24.6572 35.3236 25.4202 35.419 26.1944H36.444C36.7431 26.1944 37.0393 26.1355 37.3157 26.0211C37.592 25.9066 37.8431 25.7388 38.0546 25.5273C38.2662 25.3158 38.4339 25.0647 38.5484 24.7883C38.6629 24.512 38.7218 24.2158 38.7218 23.9167V12.5278C38.7218 11.9237 38.4818 11.3443 38.0546 10.9171C37.6275 10.49 37.0481 10.25 36.444 10.25Z"
                        fill="#9B0000" />
                      <path
                        d="M23.5634 18.0059C21.6487 18.0059 19.7771 18.5736 18.1851 19.6373C16.5932 20.701 15.3524 22.2129 14.6197 23.9818C13.887 25.7507 13.6953 27.6972 14.0688 29.575C14.4424 31.4528 15.3643 33.1778 16.7182 34.5316C18.072 35.8855 19.7969 36.8074 21.6748 37.181C23.5526 37.5545 25.4991 37.3628 27.268 36.6301C29.0369 35.8974 30.5487 34.6566 31.6125 33.0646C32.6762 31.4727 33.2439 29.601 33.2439 27.6864C33.2409 25.1199 32.22 22.6594 30.4052 20.8446C28.5904 19.0298 26.1299 18.0089 23.5634 18.0059ZM23.5634 35.0892C22.0992 35.0892 20.668 34.655 19.4506 33.8416C18.2332 33.0282 17.2844 31.872 16.7241 30.5193C16.1638 29.1667 16.0172 27.6782 16.3028 26.2422C16.5885 24.8062 17.2935 23.4872 18.3288 22.4519C19.3641 21.4166 20.6832 20.7115 22.1192 20.4259C23.5552 20.1402 25.0436 20.2868 26.3963 20.8471C27.749 21.4074 28.9051 22.3563 29.7186 23.5737C30.532 24.791 30.9662 26.2223 30.9662 27.6864C30.9631 29.6488 30.1822 31.53 28.7946 32.9176C27.407 34.3053 25.5258 35.0862 23.5634 35.0892Z"
                        fill="#9B0000" />
                    </g>
                    <defs>
                      <clipPath id="clip0_118_12443">
                        <rect width="41" height="41" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </span>
                <div class="flex flex-col text-sm leading-tight w-full">
                  <span class="text-[20px] font-semibold">120</span>
                  <span class="text-end text-[16px] text-gray-700">รายการ</span>
                </div>
              </div>
              <div class="flex items-center gap-2 bg-gray-200 p-1">
                <span>
                  <svg width="25" height="25" viewBox="0 0 38 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M32.2072 3.92461C31.5409 6.61406 29.1395 8.5 26.3885 8.5C23.6374 8.5 21.236 6.61406 20.5697 3.92461L19.9891 1.60703C19.7121 0.471484 18.5641 -0.219141 17.4294 0.0664062C16.2947 0.351953 15.6152 1.50078 15.8989 2.64297L16.4728 4.96055C17.1985 7.88242 19.1249 10.2465 21.6385 11.5746V31.875C21.6385 33.0504 22.5819 34 23.7496 34C24.9173 34 25.8607 33.0504 25.8607 31.875V23.375H26.9162V31.875C26.9162 33.0504 27.8596 34 29.0273 34C30.1951 34 31.1385 33.0504 31.1385 31.875V11.5746C33.652 10.2465 35.5784 7.88242 36.3041 4.96055L36.878 2.64297C37.1617 1.50742 36.4756 0.351953 35.3409 0.0664062C34.2062 -0.219141 33.0648 0.471484 32.7878 1.60703L32.2138 3.92461H32.2072ZM26.3885 6.375C27.2283 6.375 28.0338 6.03918 28.6276 5.4414C29.2215 4.84363 29.5551 4.03288 29.5551 3.1875C29.5551 2.34212 29.2215 1.53137 28.6276 0.933597C28.0338 0.335825 27.2283 0 26.3885 0C25.5486 0 24.7431 0.335825 24.1493 0.933597C23.5554 1.53137 23.2218 2.34212 23.2218 3.1875C23.2218 4.03288 23.5554 4.84363 24.1493 5.4414C24.7431 6.03918 25.5486 6.375 26.3885 6.375ZM5.27734 6.375C6.1172 6.375 6.92265 6.03918 7.51652 5.4414C8.11038 4.84363 8.44401 4.03288 8.44401 3.1875C8.44401 2.34212 8.11038 1.53137 7.51652 0.933597C6.92265 0.335825 6.1172 0 5.27734 0C4.43749 0 3.63204 0.335825 3.03817 0.933597C2.44431 1.53137 2.11068 2.34212 2.11068 3.1875C2.11068 4.03288 2.44431 4.84363 3.03817 5.4414C3.63204 6.03918 4.43749 6.375 5.27734 6.375ZM4.74957 8.5C2.42075 8.5 0.527344 10.4059 0.527344 12.75V31.875C0.527344 33.0504 1.47075 34 2.63845 34C3.80616 34 4.74957 33.0504 4.74957 31.875V23.375H5.80512V31.875C5.80512 33.0504 6.74852 34 7.91623 34C9.08394 34 10.0273 33.0504 10.0273 31.875V16.7809L10.885 18.1422C11.2742 18.7531 11.9471 19.1316 12.6662 19.1316H15.8329C17.0006 19.1316 17.944 18.182 17.944 17.0066C17.944 15.8313 17.0006 14.8816 15.8329 14.8816H13.8273L11.36 10.9703C10.3968 9.42969 8.7145 8.5 6.90686 8.5H4.74957Z"
                      fill="#9B0000" />
                  </svg>
                </span>
                <div class="flex flex-col text-sm leading-tight w-full">
                  <span class="text-[20px] font-semibold">120</span>
                  <span class="text-end text-[16px] text-gray-700">รายการ</span>
                </div>
              </div>
            </div>

            <!-- Row 3 -->
            <div class="grid grid-cols-6 gap-1">
              <div class="col-span-4 bg-gray-100 p-2 text-center">
                <MapSection />
              </div>
              <div class="col-span-2 bg-gray-100 p-2 text-center overflow-y-auto h-screen">
                <RightSidebar />
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </UDashboardPanel>

</template>

<script>

import Filter from '~/components/dashboard/Filter.vue';
import LeftSidebar1 from '~/components/dashboard/LeftSidebar1.vue';
import LeftSidebar2 from '~/components/dashboard/LeftSidebar2.vue';
import MapSection from '~/components/dashboard/MapSection.vue';
import RightSidebar from '~/components/dashboard/RightSidebar.vue';

export default {
  name: 'DashboardPage',
  components: {
    Filter,
    LeftSidebar1,
    LeftSidebar2,
    MapSection,
    RightSidebar,
  },
  data() {
    return {
      selectedTab: 'b1', // เริ่มต้นที่ tab แรก
    };
  }
};
</script>

<style scoped>
.date-time {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}
.dashboard-layout {
  width: 100%;
  display: grid;
  grid-template-rows: auto 1fr;
  height: 100vh;
  background-color: #f0f2f5;
  font-family: 'Arial', sans-serif;
}

.main-content {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 5px;
  padding: 5px;
  overflow: hidden;
}

.overview-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}
</style>
