<!-- app/components/UserMenu.vue -->
<script setup lang="ts">
import type { DropdownMenuItem } from "@nuxt/ui";
import { useAuthStore } from "~/stores/auth";
import { useColorMode } from '@vueuse/core'

defineProps<{
  collapsed?: boolean;
}>()

const { $keycloak } = useNuxtApp()
const { t, locale, setLocale } = useI18n()
const colorMode = useColorMode()
const appConfig = useAppConfig()
const authStore = useAuthStore()
const basePath = useRuntimeConfig().public.baseURL || "/"

// ✅ user เป็น computed reactive อัตโนมัติ
const user = computed(() => {
  const u = authStore.user
  if (!u) {
    return {
      name: "Guest",
      avatar: {
        src: "/images/default-avatar.png",
        alt: "Guest"
      }
    }
  }
  return {
    name: `${u.fullName ?? ""}` || u.username || "Guest",
    avatar: {
      src: u.avatar || "/images/default-avatar.png",
      alt: `${u?.fullName ?? ""}` || "Guest",
    },
  }
})

// ✅ Logout function
const logOut = async () => {
  try {
    await useApi("/kapi/auth/signout", { method: "POST" })

    // 1) Reset Pinia
    authStore.$reset()

    // 2) Clear LocalStorage
    localStorage.removeItem("auth")

    // 3) Keycloak logout
    if ($keycloak) {
      $keycloak.clearToken?.()
      const logoutRedirect = `${window.location.origin}${basePath === "/" ? "" : basePath}/`
      await $keycloak.logout({ redirectUri: logoutRedirect })
      return // ✅ ไม่ต้อง navigateTo ซ้ำ
    }

    // 4) ถ้าไม่ใช้ Keycloak → redirect ด้วย navigateTo
    await navigateTo(basePath, { replace: true })
  } catch (error) {
    console.error("Logout failed:", error)
  }
}


// 🎨 Theme Colors
const colors = [
  "red", "orange", "amber", "yellow", "lime", "green", "emerald",
  "teal", "cyan", "sky", "blue", "indigo", "violet", "purple",
  "fuchsia", "pink", "rose"
]
const neutrals = ["slate", "gray", "zinc", "neutral", "stone"]

// ✅ Dropdown Items
const items = computed<DropdownMenuItem[][]>(() => [
  [
    {
      type: "label",
      label: user.value.name,
      avatar: user.value.avatar,
    },
  ],
  [
    {
      label: t("nav.profile"),
      icon: "i-lucide-user",
    },
    {
      label: t("nav.setting"),
      icon: "i-lucide-settings",
      to: "/settings",
    },
  ],
  [
    {
      label: t("navSetting.language"),
      icon: "i-lucide-languages",
      children: [
        {
          label: t("navSetting.th"),
          type: "checkbox",
          checked: locale.value === "th",
          onSelect: async (e: Event) => {
            e.preventDefault()
            await setLocale("th")
          },
        },
        {
          label: t("navSetting.en"),
          type: "checkbox",
          checked: locale.value === "en",
          onSelect: async (e: Event) => {
            e.preventDefault()
            await setLocale("en")
          },
        },
      ],
    },
    {
      label: t("navSetting.theme"),
      icon: "i-lucide-palette",
      children: [
        {
          label: t("colors.primary"),
          slot: "chip",
          chip: appConfig.ui.colors.primary,
          content: {
            align: "center",
            collisionPadding: 16,
          },
          children: colors.map((color) => ({
            label: t(`colors.${color}`),
            chip: color,
            slot: "chip",
            checked: appConfig.ui.colors.primary === color,
            type: "checkbox",
            onSelect: (e: Event) => {
              e.preventDefault()
              appConfig.ui.colors.primary = color
            },
          })),
        },
        {
          label: t("colors.neutral"),
          slot: "chip",
          chip: appConfig.ui.colors.neutral,
          content: {
            align: "end",
            collisionPadding: 16,
          },
          children: neutrals.map((color) => ({
            label: t(`colors.${color}`),
            chip: color,
            slot: "chip",
            type: "checkbox",
            checked: appConfig.ui.colors.neutral === color,
            onSelect: (e: Event) => {
              e.preventDefault()
              appConfig.ui.colors.neutral = color
            },
          })),
        },
      ],
    },
    {
      label: t("navSetting.appearance"),
      icon: "i-lucide-sun-moon",
      children: [
        {
          label: t("navSetting.light"),
          icon: "i-lucide-sun",
          type: "checkbox",
          checked: colorMode.value === "light",
          onSelect: (e: Event) => {
            e.preventDefault()
            colorMode.value = "light"  // เปลี่ยนตรงนี้เท่านั้น
          },
        },
        {
          label: t("navSetting.dark"),
          icon: "i-lucide-moon",
          type: "checkbox",
          checked: colorMode.value === "dark",
          onSelect: (e: Event) => {
            e.preventDefault()
            colorMode.value = "dark"  // เปลี่ยนตรงนี้เท่านั้น
          },
        },

      ],
    },
  ],
  [
    {
      label: t("nav.logout"),
      icon: "i-lucide-log-out",
      onSelect: async (e: Event) => {
        e.preventDefault()
        await logOut()
      },
    },
  ],
])

</script>

<template>
  <UDropdownMenu :items="items" :content="{ align: 'center', collisionPadding: 12 }" :ui="{
    content: collapsed ? 'w-48' : 'w-(--reka-dropdown-menu-trigger-width)',
  }">
    <UButton :label="collapsed ? undefined : (user?.name || 'Guest')"
      :trailing-icon="collapsed ? undefined : 'i-lucide-chevrons-up-down'" color="neutral" variant="ghost" block
      :square="collapsed" class="data-[state=open]:bg-elevated" :ui="{ trailingIcon: 'text-dimmed' }" />

    <template #chip-leading="{ item }">
      <span :style="{
        backgroundColor: colorMode === 'dark'
          ? `var(--color-${(item as any).chip}-400)`
          : `var(--color-${(item as any).chip}-500)`,
      }" class="ms-0.5 size-2 rounded-full" />
    </template>
  </UDropdownMenu>
</template>
