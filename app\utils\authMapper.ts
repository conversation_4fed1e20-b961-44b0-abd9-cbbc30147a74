// app/utils/authMapper.ts
import type { Profile, UserData } from '~/types'

export function mapProfileToUserData(
    profile: Profile,
    token: string | undefined,
    refreshToken?: string
): UserData {
    return {
        id: profile.sub,
        username: profile.username,
        email: profile.email,
        firstName: profile.givenName,
        lastName: profile.familyName,
        districts: [],
        permissions: profile.permissions || [],
        role: profile.role,
        token: token ?? '',              // ป้องกัน undefined
        refreshToken: refreshToken ?? '',// ป้องกัน undefined
        avatar: profile.avatar,
        locale: profile.locale || 'en',
    }
}
