<!-- app/layouts/landing.vue -->
<script setup lang="ts">
const { t } = useI18n()
</script>

<template>
  <div class="min-h-screen flex flex-col bg-white dark:bg-gray-950">
    <SaasAppHeader />

    <main class="flex-1">
      <slot />
    </main>

    <footer class="py-8 text-center text-gray-500 dark:text-gray-400 text-sm">
      {{ t("landing.klynx.copyright") }}
    </footer>
  </div>
</template>
