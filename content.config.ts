// content.config.ts
import { defineCollection, defineContentConfig, z } from '@nuxt/content'

const variantEnum = z.enum(['solid', 'outline', 'subtle', 'soft', 'ghost', 'link'])
const colorEnum = z.enum(['primary', 'secondary', 'neutral', 'error', 'warning', 'success', 'info'])
const sizeEnum = z.enum(['xs', 'sm', 'md', 'lg', 'xl'])
const orientationEnum = z.enum(['vertical', 'horizontal'])

const createBaseSchema = () => z.object({
    title: z.string().nonempty(),
    description: z.string().nonempty()
})

const createFeatureItemSchema = () => createBaseSchema().extend({
    icon: z.string().nonempty().editor({ input: 'icon' })
})

const createLinkSchema = () => z.object({
    label: z.string().nonempty(),
    to: z.string().nonempty(),
    icon: z.string().optional().editor({ input: 'icon' }),
    size: sizeEnum.optional(),
    trailing: z.boolean().optional(),
    target: z.string().optional(),
    color: colorEnum.optional(),
    variant: variantEnum.optional()
})

const createImageSchema = () => z.object({
    src: z.string().nonempty().editor({ input: 'media' }),
    alt: z.string().optional(),
    loading: z.string().optional(),
    srcset: z.string().optional()
})

export default defineContentConfig({
    collections: {
        index: defineCollection({
            source: 'index.yml',
            type: 'page',
            schema: z.object({
                hero: z.object({ links: z.array(createLinkSchema()) }),
                sections: z.array(
                    createBaseSchema().extend({
                        id: z.string().nonempty(),
                        orientation: orientationEnum.optional(),
                        reverse: z.boolean().optional(),
                        features: z.array(createFeatureItemSchema())
                    })
                ),
                features: createBaseSchema().extend({
                    items: z.array(createFeatureItemSchema())
                }),
                testimonials: createBaseSchema().extend({
                    headline: z.string().optional(),
                    items: z.array(z.object({
                        quote: z.string().nonempty(),
                        user: z.object({
                            name: z.string().nonempty(),
                            description: z.string().nonempty(),
                            to: z.string().nonempty(),
                            target: z.string().nonempty(),
                            avatar: createImageSchema()
                        })
                    }))
                }),
                cta: createBaseSchema().extend({
                    links: z.array(createLinkSchema())
                })
            })
        }),

        docs: defineCollection({
            source: 'docs/**/*',
            type: 'page'
        }),

        pricing: defineCollection({
            source: 'pricing.yml',
            type: 'page',
            schema: z.object({
                plans: z.array(z.object({
                    title: z.string().nonempty(),
                    description: z.string().nonempty(),
                    price: z.object({ month: z.string().nonempty(), year: z.string().nonempty() }),
                    billing_period: z.string().nonempty(),
                    billing_cycle: z.string().nonempty(),
                    button: createLinkSchema(),
                    features: z.array(z.string().nonempty()),
                    highlight: z.boolean().optional()
                })),
                logos: z.object({
                    title: z.string().nonempty(),
                    icons: z.array(z.string())
                }),
                faq: createBaseSchema().extend({
                    items: z.array(z.object({
                        label: z.string().nonempty(),
                        content: z.string().nonempty(),
                        defaultOpen: z.boolean().optional()
                    }))
                })
            })
        }),

        // ไฟล์ singleton metadata ของ changelog (ถ้ามี changelog.yml)
        changelog: defineCollection({
            source: 'changelog.yml',
            type: 'page'          // ✅ เดิมโอเค
        }),

        // รายการเวอร์ชัน: ตรงกับโฟลเดอร์ content/changelog/*.md
        versions: defineCollection({
            source: 'changelog/**/*',
            type: 'page',         // ✅ เปลี่ยนจาก 'content' เป็น 'page'
            schema: z.object({
                title: z.string().nonempty(),
                description: z.string().optional(),
                date: z.coerce.date(),   // ✅ รับ string แล้วแปลงเป็น Date
                image: z.string().optional()
            })
        })
    }
})
