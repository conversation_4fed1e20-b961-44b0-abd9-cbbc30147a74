<script>
export default {
  name: '<PERSON><PERSON><PERSON><PERSON>',
};
</script>

<template>
  <header class="header">
    <div class="logo">Dashboard</div>
    <div class="date-time">
      <!-- <span>วันที่: 11 เมษายน 2568</span> -->
      <!-- <span>10:36 น.</span> -->
      <svg width="27" height="26" viewBox="0 0 27 26" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M25.9943 22.9648C25.1639 22.2246 24.4369 21.3759 23.8329 20.4418C23.1729 19.1525 22.7776 17.7442 22.6704 16.2998V12.0459C22.6761 9.77742 21.8532 7.58492 20.3564 5.88035C18.8595 4.17578 16.7917 3.07647 14.5415 2.78898V1.67815C14.5415 1.37326 14.4204 1.08086 14.2048 0.86527C13.9892 0.649681 13.6968 0.528564 13.3919 0.528564C13.0871 0.528564 12.7947 0.649681 12.5791 0.86527C12.3635 1.08086 12.2424 1.37326 12.2424 1.67815V2.8062C10.0123 3.11442 7.96951 4.22037 6.49232 5.91921C5.01513 7.61806 4.20366 9.79467 4.2082 12.0459V16.2998C4.10104 17.7442 3.70577 19.1525 3.0457 20.4418C2.45229 21.3738 1.73698 22.2223 0.918754 22.9648C0.826901 23.0455 0.753284 23.1448 0.702803 23.2562C0.652321 23.3676 0.626131 23.4884 0.625977 23.6106V24.7818C0.625977 25.0101 0.7167 25.2292 0.87819 25.3907C1.03968 25.5521 1.25871 25.6429 1.48709 25.6429H25.426C25.6544 25.6429 25.8734 25.5521 26.0349 25.3907C26.1964 25.2292 26.2871 25.0101 26.2871 24.7818V23.6106C26.2869 23.4884 26.2607 23.3676 26.2103 23.2562C26.1598 23.1448 26.0862 23.0455 25.9943 22.9648ZM2.41709 23.9206C3.21809 23.1465 3.92348 22.2792 4.5182 21.3373C5.34985 19.7804 5.83456 18.0619 5.93903 16.2998V12.0459C5.90488 11.0367 6.07416 10.031 6.43679 9.08858C6.79942 8.14618 7.34799 7.28638 8.04983 6.56039C8.75166 5.8344 9.59242 5.25707 10.522 4.86277C11.4516 4.46848 12.4511 4.26528 13.4608 4.26528C14.4706 4.26528 15.4701 4.46848 16.3997 4.86277C17.3293 5.25707 18.17 5.8344 18.8718 6.56039C19.5737 7.28638 20.1222 8.14618 20.4849 9.08858C20.8475 10.031 21.0168 11.0367 20.9826 12.0459V16.2998C21.0871 18.0619 21.5718 19.7804 22.4035 21.3373C22.9982 22.2792 23.7036 23.1465 24.5046 23.9206H2.41709Z"
          fill="#9B0000" />
      </svg>
      <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M26.1998 5C23.4998 2.5 19.9498 1 15.9998 1C12.0498 1 8.4998 2.5 5.7998 5H26.1998ZM5.7998 27C8.4998 29.5 12.0498 31 15.9998 31C19.9498 31 23.4998 29.5 26.1998 27H5.7998Z"
          fill="#ED4C5C" />
        <path
          d="M1 16C1 18.15 1.45 20.15 2.25 22H29.75C30.55 20.15 31 18.15 31 16C31 13.85 30.55 11.85 29.75 10H2.25C1.45 11.85 1 13.85 1 16Z"
          fill="#2A5F9E" />
        <path
          d="M5.8002 27H26.1502C27.6502 25.6 28.9002 23.9 29.7002 22H2.2002C3.1002 23.9 4.3002 25.6 5.8002 27ZM26.2002 5H5.8002C4.3002 6.4 3.0502 8.1 2.2502 10H29.7502C28.9002 8.1 27.7002 6.4 26.2002 5Z"
          fill="#F9F9F9" />
      </svg>

    </div>
  </header>
</template>

<style scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: #333;
}

.logo {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
  /* Example blue */
}

.nav-controls {
  display: flex;
  gap: 10px;
}

.nav-button {
  display: flex;
  align-items: center;
  padding: 8px 15px;
  border: none;
  border-radius: 5px;
  background-color: #eee;
  cursor: pointer;
  font-size: 14px;
  color: #555;
  transition: background-color 0.3s;
}

.nav-button.active {
  background-color: #007bff;
  color: #fff;
}

.nav-button i {
  margin-right: 5px;
}

.date-time {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.flag-icon {
  width: 24px;
  height: 18px;
}
</style>