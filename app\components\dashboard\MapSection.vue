<template>
  <section class="map-section">
    <div id="map-container" ref="mapContainer"></div>

    <div class="map-overlay-card" v-if="selectedLocation">
      <h4>{{ selectedLocation.title }}</h4>
      <p>{{ selectedLocation.description }}</p>
      <button @click="viewLiveStream(selectedLocation.id)">Live Stream</button>
    </div>

    <div class="alert-list-container">
      <AlertCard
        v-for="alert in activeAlerts"
        :key="alert.id"
        :alert="alert"
        style="margin-bottom: 5px;"
      />
      <div v-if="activeAlerts.length === 0" class="no-alerts-message">
        ไม่มีการแจ้งเตือนขณะนี้
      </div>
    </div>
  </section>
</template>

<script>
import AlertCard from '~/components/AlertCard.vue';

export default {
  name: 'MapSection',
  components: {
    AlertCard
  },
  data() {
    return {
      map: null,
      selectedLocation: null,
      markers: [],
      cameraDefinitions: [
        {
          id: 1,
          lat: 17.40,
          lng: 102.77,
          title: 'กล้อง 1',
          description: 'บ้านลิ้นจี่',
        },
        {
          id: 2,
          lat: 17.41,
          lng: 102.78,
          title: 'กล้อง 2',
          description: 'บ้านคำป่าโมง',
        },
        {
          id: 3,
          lat: 17.395,
          lng: 102.76,
          title: 'กล้อง 3',
          description: 'ถนนอุดร-ขอนแก่น',
        },
      ],
      activeAlerts: [],
      alertInterval: null,
    };
  },
  mounted() {
    this.loadGoogleMapsScript()
      .then(() => {
        this.initMap();
        this.startAlertPolling();
      })
      .catch((err) => {
        console.error('ไม่สามารถโหลด Google Maps ได้:', err);
      });
  },
  beforeUnmount() {
    if (this.alertInterval) {
      clearInterval(this.alertInterval);
    }
  },
  methods: {
    loadGoogleMapsScript() {
      return new Promise((resolve, reject) => {
        if (window.google && window.google.maps) {
          resolve();
          return;
        }

        const script = document.createElement('script');
        script.src = 'https://maps.googleapis.com/maps/api/js?key=';
        script.async = true;
        script.defer = true;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });
    },
    initMap() {
      this.map = new google.maps.Map(this.$refs.mapContainer, {
        center: { lat: 17.40, lng: 102.77 },
        zoom: 13,
      });

      this.cameraDefinitions.forEach((location) => {
        const marker = new google.maps.Marker({
          position: { lat: location.lat, lng: location.lng },
          map: this.map,
          title: location.title,
          icon: {
            url: '/images/camera_active.png',
            scaledSize: new google.maps.Size(60, 61),
          },
        });

        marker.addListener('click', () => {
          this.selectedLocation = location;
        });

        this.markers.push({ id: location.id, marker: marker });
      });

      this.$watch('activeAlerts', this.updateMarkerIcons, { deep: true });
    },

    fetchNewAlerts() {
      console.log('Fetching new alerts...');
      const possibleAlerts = [
        { id: 1, status: false, detectedEvent: 'เข้าพื้นที่เฝ้าระวัง', detectedObject: 'บุคคล' },
        { id: 2, status: false, detectedEvent: 'การเคลื่อนไหวผิดปกติ', detectedObject: 'ยานพาหนะ' },
        { id: 3, status: false, detectedEvent: 'การบุกรุก', detectedObject: 'บุคคลไม่ทราบชื่อ' },
        { id: 1, status: true, detectedEvent: 'กลับสู่สภาวะปกติ', detectedObject: '-' }, 
        { id: 2, status: true, detectedEvent: 'กลับสู่สภาวะปกติ', detectedObject: '-' },
        { id: 3, status: true, detectedEvent: 'กลับสู่สภาวะปกติ', detectedObject: '-' },
      ];

      const randomAlertIndex = Math.floor(Math.random() * possibleAlerts.length);
      const newAlert = JSON.parse(JSON.stringify(possibleAlerts[randomAlertIndex]));

      const cameraDef = this.cameraDefinitions.find(cam => cam.id === newAlert.id);
      if (cameraDef) {
        newAlert.description = cameraDef.description;
        newAlert.dateTime = new Date().toISOString();
      } else {
        newAlert.description = 'Unknown Camera';
        newAlert.dateTime = new Date().toISOString();
      }

      const existingAlertIndex = this.activeAlerts.findIndex(alert => alert.id === newAlert.id);

      if (newAlert.status === false) {
        if (existingAlertIndex === -1) {
          this.activeAlerts.unshift(newAlert);
        } else {
          this.activeAlerts[existingAlertIndex] = newAlert;
          const [movedAlert] = this.activeAlerts.splice(existingAlertIndex, 1);
          this.activeAlerts.unshift(movedAlert);
        }
      } else {
        if (existingAlertIndex !== -1) {
          this.activeAlerts.splice(existingAlertIndex, 1);
        }
      }
      console.log('Current Active Alerts:', this.activeAlerts);
    },
    startAlertPolling() {
      this.fetchNewAlerts();
      this.alertInterval = setInterval(this.fetchNewAlerts, 3000);
    },
    updateMarkerIcons() {
      this.markers.forEach(markerObj => {
        const marker = markerObj.marker;
        const alertStatus = this.activeAlerts.find(alert => alert.id === markerObj.id);

        let iconUrl = '/images/camera_active.png'; 
        if (alertStatus && !alertStatus.status) { 
          iconUrl = '/images/camera_no_active.png'; 
        }

        marker.setIcon({
          url: iconUrl,
          scaledSize: new google.maps.Size(60, 61),
        });
      });
    },
    viewLiveStream(id) {
      console.log('ดูไลฟ์กล้อง ID:', id);
      // เพิ่มโค้ดเปิด modal หรือเปลี่ยนหน้าได้ที่นี่
    },
  },
};
</script>


<style scoped>
.map-section {
  position: relative;
  width: 100%;
  height: 500px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#map-container {
  width: 100%;
  height: 100%;
}

.map-overlay-card {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.95);
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 10;
  max-width: 250px; 
}

.alert-list-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: calc(100% - 40px);
  overflow-y: auto;
  padding-right: 5px;
}

.no-alerts-message {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 10px;
  border-radius: 8px;
  text-align: center;
  color: #666;
  font-size: 0.9em;
}
</style>