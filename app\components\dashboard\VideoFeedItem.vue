<template>
  <div class="video-feed-item">
    <img :src="feed.thumbnail" alt="Video Thumbnail" class="thumbnail" />
    <div class="feed-details">
      <p class="feed-name">{{ feed.name }}</p>
      </div>
  </div>
</template>

<script>
export default {
  name: 'VideoFeedItem',
  props: {
    feed: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style scoped>
.video-feed-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 5px;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: background-color 0.2s;
}

.video-feed-item:hover {
  background-color: #f0f0f0;
}

.thumbnail {
  width: 80px; /* Adjust size as needed */
  height: 60px;
  object-fit: cover;
  border-radius: 3px;
}

.feed-name {
  font-size: 14px;
  color: #333;
  margin: 0;
}
</style>