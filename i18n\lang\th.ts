export default {
  global: {
    title: 'ระบบศูนย์ปฏิบัติการอัจฉริยะ',
    description: 'ระบบศูนย์ปฏิบัติการอัจฉริยะ',
    login: 'ระบบศูนย์ปฏิบัติการอัจฉริยะ',
    copyright: 'ลิขสิทธิ์ © 2567 พอยท์ ไอที คอนซัลทิ่ง จำกัด',
    headersBrand: 'ระบบศูนย์ปฏิบัติการอัจฉริยะ',
    loading: 'กำลังตรวจสอบการเข้าสู่ระบบ...'
  },
  landing: {
    klynx: {
      main: "ระบบศูนย์ปฏิบัติการอัจฉริยะ",
      login: "เข้าสู่ระบบ",
      copyright: "ลิขสิทธิ์ © 2567 พอยท์ ไอที คอนซัลทิ่ง จำกัด",
      nextgen: "แพลตฟอร์ม IOC",
      description: "สร้างและจัดการทรัพยากรของคุณด้วย Klynx IOC ปลอดภัย ขยายขนาดได้ และรวดเร็ว",
      getstart: "เริ่มต้นใช้งาน"
    },
    home: {
      main: "หน้าแรก"
    },
    docs: {
      main: "เอกสาร"
    },
    pricing: {
      main: "ราคา"
    },
    changelog: {
      main: "บันทึกการเปลี่ยนแปลง"
    }
  },
  nav: {
    approve: 'การอนุมัติ',
    report: 'รายงาน',
    setting: 'การตั้งค่า',
    profile: 'โปรไฟล์',
    logout: 'ออกจากระบบ',
    login: 'เข้าสู่ระบบ',
    svms: 'K-Super vms',
    iboc: 'AI Platform',
    main: 'หน้าแรก',

    api: {
      main: 'API',
    },
    menu: {
      main: 'เมนู',
    },
    permissions: {
      main: 'สิทธิ์',
      all: 'สิทธิ์ทั้งหมด',
      create: 'สร้างสิทธิ์',
      edit: 'แก้ไขสิทธิ์',
      delete: 'ลบสิทธิ์',
      menu: 'การเข้าถึงเมนู',
      api: 'การเข้าถึง API',
      resource: 'การเข้าถึงทรัพยากร'
    },
    users: {
      title: 'การจัดการผู้ใช้',
      main: 'ผู้ใช้',
      all: 'รายชื่อสมาชิก',
      approvel: 'กลุ่มการอนุมัติ',
      role: 'บทบาท',
      organizations: 'องค์กร'
    },
    watchman: {
      main: 'ศูนย์ข้อมูลข่าวสารประจำสถานีตำรวจ',
      dashboard: 'ภาพรวม',
      events: 'เหตุกาณ'
    },
  },
  navSetting: {
    title: 'การตั้งค่าระบบ',
    theme: 'ธีม',
    appearance: 'รูปแบบ',
    light: 'สว่าง',
    dark: 'มึด',
    site: 'การตั้งค่าไซต์',
    organization: 'การตั้งค่าหน่วยงาน',
    website: 'แม่แบบเว็บไซต์',
    login: 'หน้าเข้าสู่ระบบ',
    email: 'การตั้งค่าอีเมล',
    notifications: 'การตั้งค่าการแจ้งเตือน',
    line: 'การตั้งค่าไลน์',
    telegram: 'การตั้งค่าเทเลแกรม',
    api: 'การตั้งค่า API',
    sms: 'การตั้งค่า SMS',
    module: 'โมดูล',
    language: 'ภาษา',
    th: 'ภาษาไทย',
    en: 'ภาษาอังกฤษ',
    department: 'แผนก',
    cookie: 'นโยบายคุกกี้',
    history: 'ประวัติการใช้งาน',
    devices: {
      title: 'การจัดการอุปกรณ์',
      cameras: 'กล้องวงจรปิด',
      kControls: 'อุปกรณ์ควบคุม',
      speakers: 'ลำโพง',
      aiTracker: 'AI Tracker'
    },
    vehicle: {
      title: 'บริหารจัดการยานพาหนะ',
      list: 'รายการยานพาหนะ',
      accessories: 'อุปกรณ์เสริม',
      types: 'ประเภทของยานพาหนะ',
      brand: 'ยี่ห้อของยานพาหนะ'
    }
  },
  colors: {
    primary: 'สีหลัก',
    neutral: 'สีกลาง',
    red: 'แดง',
    orange: 'ส้ม',
    amber: 'เหลืองอำพัน',
    yellow: 'เหลือง',
    lime: 'เขียวมะนาว',
    green: 'เขียว',
    emerald: 'เขียวมรกต',
    teal: 'เขียวน้ำทะเล',
    cyan: 'ฟ้าน้ำเงิน',
    sky: 'ฟ้า',
    blue: 'น้ำเงิน',
    indigo: 'คราม',
    violet: 'ม่วงอ่อน',
    purple: 'ม่วง',
    fuchsia: 'ชมพูม่วง',
    pink: 'ชมพู',
    rose: 'กุหลาบ',
    slate: 'เทาเข้ม',
    gray: 'เทา',
    zinc: 'เทาเงิน',
    stone: 'หิน'
  },
  systemUsers: {
    title: "ผู้ใช้ระบบ",
    table: {
      username: 'ชื่อผู้ใช้',
      fullname: 'ชื่อ-นามสกุล',
      email: 'อีเมล',
      phone: 'โทรศัพท์',
      role: 'บทบาท',
      status: 'สถานะ',
      district: 'เขต/อำเภอ',
      location: 'สถานที่ทำงาน',
      createdAt: 'วันที่สร้าง',
      order: 'ลำดับ',
      searchPlaceholder: 'ค้นหาชื่อผู้ใช้, ชื่อ-นามสกุล, อีเมล',
      actions: 'การกระทำ'
    },
    status: {
      active: 'เปิดใช้งาน',
      inactive: 'ปิดใช้งาน'
    },
    searchPlaceholder: "ค้นหาผู้ใช้...",
    pagination: {
      "showing": "แสดง",
      "of": "จากทั้งหมด",
      "users": "ผู้ใช้"
    },
    form: {
      titleCreate: 'สร้างผู้ใช้',
      titleEdit: 'แก้ไขผู้ใช้',
      username: 'ชื่อผู้ใช้',
      fullname: 'ชื่อ-นามสกุล',
      email: 'อีเมล',
      phone: 'โทรศัพท์',
      role: 'บทบาท',
      district: 'เขต/อำเภอ',
      location: 'สถานที่ทำงาน',
      status: 'สถานะ',
      submit: 'บันทึก',
      createUser: 'สร้างผู้ใช้',
      cancel: 'ยกเลิก'
    },
    messages: {
      created: '✅ ผู้ใช้ถูกสร้างเรียบร้อยแล้ว',
      updated: '✅ ผู้ใช้ถูกแก้ไขเรียบร้อยแล้ว',
      deleted: '🗑 ผู้ใช้ถูกลบเรียบร้อยแล้ว',
      confirmDelete: 'คุณแน่ใจหรือไม่ว่าต้องการลบผู้ใช้นี้?'
    }
  },
  dashboard: {
    title: 'แดชบอร์ด',
    stats: {
      pending: 'การจองที่รอการตรวจสอบ',
      approved: 'การจองที่ได้รับการอนุมัติ',
      rejected: 'การจองที่ไม่อนุมัติ',
      vehicle: 'ยานพาหนะทั้งหมด'
    }
  },
  filter: {
    dateStart: 'จาก',
    dateEnd: 'ถึง',
    area: 'กำหนดพื้นที่แสดงอุปกรณ์',
    deviceType: 'เลือกประเภทอุปกรณ์ทั้งหมด',
    device: 'เลือกอุปกรณ์ทั้งหมด',
    todayDate: 'วันที่'
  }
  ,
  watchman: {
    title: 'บันทึกข้อมูลข่าวสารประจำสถานีตำรวจ',
    table: {
      "order": 'ลำดับ',
      "image": 'รูปภาพ',
      "prefix": 'คำนำหน้า',
      "fname": 'ชื่อ',
      "lname": 'นามสกุล',
      "nickname": 'ชื่อเล่น',
      "age": 'อายุ',
      "policeStation": 'สถานีตำรวจ',
      "action": 'ดำเนเการ',
    }
  },
  kwatch: {
    main: "ระบบสืบค้น",
  },
  police: {
    title: 'kwatch',
    table: {
      "order": 'ลำดับ',
      "image": 'รูปภาพ',
      "prefix": 'คำนำหน้า',
      "fname": 'ชื่อ',
      "lname": 'นามสกุล',
      "nickname": 'ชื่อเล่น',
      "age": 'อายุ',
      "policeStation": 'สถานีตำรวจ',
      "action": 'Action',
    }
  },
  map: {
    title: 'Map',
    table: {
      "order": 'ลำดับ',
      "image": 'รูปภาพ',
      "prefix": 'คำนำหน้า',
      "fname": 'ชื่อ',
      "lname": 'นามสกุล',
      "nickname": 'ชื่อเล่น',
      "age": 'อายุ',
      "policeStation": 'สถานีตำรวจ',
      "action": 'Action',
    }
  }
}
