// app/plugins/guard-on-ready.client.ts
import { defineNuxtPlugin } from '#app'
import { useAuthStore } from '~/stores/auth'

const PUBLIC_ROUTES = new Set(['/', '/login', '/auth/callback', '/silent-check-sso.html'])
const PUBLIC_PREFIXES = ['/docs', '/changelog']

export default defineNuxtPlugin(() => {
    const auth = useAuthStore()
    const router = useRouter()
    const route = useRoute()

    const isPublic = (path: string, meta: Record<string, any>) =>
        meta.public === true ||
        PUBLIC_ROUTES.has(path) ||
        PUBLIC_PREFIXES.some(p => path === p || path.startsWith(p + '/'))

    watch(
        [() => auth.ready, () => auth.isAuthenticated, () => route.fullPath],
        async () => {
            if (!auth.ready) return
            if (!auth.isAuthenticated && !isPublic(route.path, route.meta)) {
                if (route.path !== '/') await router.replace('/')
            }
        },
        { immediate: true }
    )
})
