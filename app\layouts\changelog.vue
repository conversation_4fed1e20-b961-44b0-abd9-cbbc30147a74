<!-- app/layouts/changelogs.vue -->
<script setup lang="ts">
const { t } = useI18n()
const route = useRoute()

const { data: page } = await useAsyncData('changelog', () => queryCollection('changelog').first())
const { data: versions } = await useAsyncData(route.path, () => queryCollection('versions').order('date', 'DESC').all())

const title = page.value?.seo?.title || page.value?.title
const description = page.value?.seo?.description || page.value?.description

useSeoMeta({
  title,
  ogTitle: title,
  description,
  ogDescription: description
})

// defineOgImageComponent('Saas')
</script>

<template>
  <SaasAppHeader />
  <UContainer>
    <UPageBody>
      <UChangelogVersions>
        <UChangelogVersion
          v-for="(version, index) in versions"
          :key="index"
          v-bind="version"
        >
          <template #body>
            <ContentRenderer :value="version.body" />
          </template>
        </UChangelogVersion>
      </UChangelogVersions>
      <UFooter>
        {{ t("landing.klynx.copyright") }}
      </UFooter>
    </UPageBody>
  </UContainer>
</template>
