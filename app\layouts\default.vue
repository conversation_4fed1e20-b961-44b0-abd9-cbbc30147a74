<!-- app/layouts/default.vue -->
<script setup lang="ts">
import type { NavigationMenuItem } from "@nuxt/ui"
import { useAuthStore } from "~/stores/auth"
import { usePermission } from '~/composables/UsePermission'

const router = useRouter()
const { hasPermission } = usePermission()
const { t } = useI18n()
const authStore = useAuthStore()
const getUser = async () => authStore.user;
const iframeUrl = ref<string | null>(null)

const route = useRoute()
const open = ref(false) // toggle sidebar
const config = useRuntimeConfig().public

// ✅ URL SVMS
// const svmsUrl = computed(() =>
//   `${config.svmsURL}/#/login?username=${encodeURIComponent(authStore.user?.username || '')}` +
//   `&password=YWRtaW4%3D&address=panel&local=${encodeURIComponent(authStore.user?.locale || '')}`
// )
// ✅ URL SVMS
const svmsPasswordB64 = computed(() => {
  const plain = config.svmsPassword || ''
  if (!plain) return ''
  // browser
  if (typeof window !== 'undefined' && typeof window.btoa === 'function') {
    return window.btoa(plain)            // admin -> YWRtaW4=
  }
  // SSR fallback (กรณีรันฝั่ง server)
  return Buffer.from(plain, 'utf-8').toString('base64')
})

// ✅ URL SVMS (encode แค่ตอนใส่ query)
const svmsUrl = computed(() =>
  `${config.svmsURL}/#/login` +
  `?username=${encodeURIComponent(authStore.user?.username || '')}` +
  `&password=${encodeURIComponent(svmsPasswordB64.value)}` +
  `&address=panel&local=${encodeURIComponent(authStore.user?.locale || 'th')}`
)

// ✅ 1) Raw menu data
const rawLinks = computed<NavigationMenuItem[][]>(() => [
  [
    // {
    //   label: t("nav.main"),
    //   icon: "i-lucide-home",
    //   to: "/dashboard",
    //   onSelect: () => {
    //     open.value = false;
    //   },
    // },
    // {
    //   label: t("Watchman.title"),
    //   icon: "i-heroicons-user-group",
    //   to: "/watchman",
    //   onSelect: () => {
    //     open.value = false;
    //   },
    // },
    {
      label: t("map.title"),
      icon: "i-heroicons-map",
      to: "/map",
      onSelect: () => {
        open.value = false;
      },
    },
    {
      label: t("police.title"),
      icon: "i-heroicons-user-group",
      to: "/police",
      onSelect: () => {
        open.value = false;
      },
    },
    {
      label: t("nav.watchman.main"),
      icon: "i-lucide-eye",
      onSelect: () => {
        const token = authStore.user?.token
        const url = `${config.watchmanURL}/WatchmanData/api_access.php?token=${token || ''}`
        router.push({ path: '/watchman', query: { url } })
        open.value = false
      },
      show: hasPermission({ any: ['menu_watchman', 'administrator', 'management', 'user'] })
    },
    {
      label: t("nav.users.title"),
      icon: 'i-heroicons-user-group',
      children: [
        {
          label: t("nav.users.main"),
          to: '/systemUsers',
          exact: true
        },
        // {
        //   label: t("nav.users.orcanizations"),
        //   to: '/organizations-groups',
        //   exact: true
        // },
        {
          label: t("nav.permissions.main"),
          to: '/systemUsers/permissions',
          exact: true
        },
      ]
    },
    // {
    //   label: t("navSetting.title"),
    //   icon: 'i-heroicons-cog-8-tooth',
    //   children: [
    //     {
    //       label: t("navSetting.history"),
    //       to: '/settings/history',
    //       exact: true
    //     },
    //     {
    //       label: t("navSetting.notifications"),
    //       to: '/settings/notifications',
    //       exact: true
    //     },
    //     {
    //       label: t("navSetting.devices.title"),
    //       children: [
    //         {
    //           label: t("navSetting.devices.cameras"),
    //           to: '/settings/sensors/devices',
    //           exact: true
    //         },
    //         {
    //           label: t("navSetting.devices.kControls"),
    //           to: '/settings/sensors/kcontrol',
    //           exact: true
    //         },
    //         {
    //           label: t("navSetting.devices.speakers"),
    //           to: '/settings/sensors/speaker',
    //           exact: true
    //         },
    //         {
    //           label: t("navSetting.devices.aiTracker"),
    //           to: '/settings/sensors/ai-tracker',
    //           exact: true
    //         }
    //       ]
    //     }
    //   ]
    // }
  ],
  [
    {
      label: t("nav.svms"),
      icon: "i-lucide-cctv",
      to: svmsUrl.value,
      target: "_blank",
      show: hasPermission({ any: ['menu_svms', 'administrator'] }),
    },
    {
      label: t("nav.iboc"),
      icon: "i-lucide-bot",
      to: `${config.ibocURL}`,
      target: "_blank",
      show: hasPermission({ any: ['menu_iboc', 'administrator'] }),
    },
  ]
])

// ✅ 2) Recursive filter function
function filterMenu(items: NavigationMenuItem[]): NavigationMenuItem[] {
  return items
    .filter(item => item.show !== false)
    .map(item => ({
      ...item,
      children: item.children ? filterMenu(item.children) : undefined
    }))
}

const links = computed(() => rawLinks.value.map(group => filterMenu(group)))

// ✅ 3) Logout function
const logOut = async () => {
  try {
    const user = await getUser();
    await useApi("/kapi/auth/signout", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${user?.token}`,
      },
    });
    await navigateTo("/");
  } catch (error) {
    console.error("Logout failed:", error);
  }
};

</script>

<template>
  <UDashboardGroup unit="rem">
    <!-- ✅ ซ่อน sidebar จนกว่า authStore.ready -->
    <UDashboardSidebar v-if="authStore.ready" id="default" v-model:open="open" collapsible resizable
      class="bg-elevated/25" :ui="{ footer: 'lg:border-t lg:border-default' }">

      <template #header="{ collapsed }">
        <div class="flex items-center gap-2 px-2 py-2 transition-all duration-200 min-h-[48px] overflow-hidden">
          <NuxtImg src="/images/logo.webp" :width="collapsed ? 28 : 38" :height="collapsed ? 28 : 38"
            class="transition-all duration-200 dark:hidden object-contain" alt="Klynx logo" loading="eager" />
          <NuxtImg src="/images/logo-dark.webp" :width="collapsed ? 28 : 38" :height="collapsed ? 28 : 38"
            class="transition-all duration-200 hidden dark:block object-contain" alt="Klynx logo dark"
            loading="eager" />

          <div v-if="!collapsed" class="flex-1 min-w-0">
            <span class="block text-xl font-bold whitespace-normal break-words">
              {{ t('global.headersBrand') }}
            </span>
          </div>
        </div>
      </template>

      <template #default="{ collapsed }">
        <UNavigationMenu :collapsed="collapsed" :items="links[0]" orientation="vertical" tooltip popover />
        <UNavigationMenu :collapsed="collapsed" :items="links[1]" orientation="vertical" tooltip class="mt-auto" />
      </template>

      <template #footer="{ collapsed }">
        <UserMenu :collapsed="collapsed" />
      </template>
    </UDashboardSidebar>

    <slot />
    <NotificationsSlideover />
  </UDashboardGroup>
</template>
