<!-- app/app.vue -->
<script setup lang="ts">
import AuthLoading from '~/components/AuthLoading.vue'
import { useAuthStore } from '~/stores/auth'
const auth = useAuthStore()
const route = useRoute()

// กำหนดหน้าที่เป็น public แบบคงที่ + แบบ prefix
const PUBLIC_ROUTES = new Set<string>([
  '/', '/login', '/auth/callback', '/silent-check-sso.html'
])
const PUBLIC_PREFIXES = ['/docs', '/changelog', '/lk', '/ki5'] // 👈 เพิ่มได้ตามต้องการ

const isPublicRoute = computed(() =>
  route.meta.public === true ||                                   // เคารพ meta public ของเพจ
  PUBLIC_ROUTES.has(route.path) ||
  PUBLIC_PREFIXES.some(p => route.path === p || route.path.startsWith(p + '/'))
)

const colorMode = useColorMode()
const color = computed(() => colorMode.value === 'dark' ? '#020618' : 'white')

useHead({
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { key: 'theme-color', name: 'theme-color', content: color }
  ],
  link: [{ rel: 'icon', href: '/favicon.ico' }],
  htmlAttrs: { lang: 'th' }
})

useSeoMeta({
  titleTemplate: 'Klynx',
  ogImage: 'https://assets.hub.nuxt.com/eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJodHRwczovL3NhYXMtdGVtcGxhdGUubnV4dC5kZXYiLCJpYXQiOjE3Mzk0NjM0NDh9.tgzUQaw6XswUPPVbOXazuWwoTHJODg155CYt1xfzIdM.jpg?theme=light',
  twitterImage: 'https://assets.hub.nuxt.com/eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJodHRwczovL3NhYXMtdGVtcGxhdGUubnV4dC5kZXYiLCJpYXQiOjE3Mzk0NjM0NDh9.tgzUQaw6XswUPPVbOXazuWwoTHJODg155CYt1xfzIdM.jpg?theme=light',
  twitterCard: 'summary_large_image'
})

// const { data: navigation } = await useAsyncData('navigation', () =>
//   queryCollectionNavigation('content')
// )
// const { data: files } = useLazyAsyncData('search', () =>
//   queryCollectionSearchSections('content')
// )

const links = [
  { label: 'Docs', icon: 'i-lucide-book', to: '/docs/getting-started' },
  { label: 'Pricing', icon: 'i-lucide-credit-card', to: '/pricing' },
  { label: 'Blog', icon: 'i-lucide-pencil', to: '/blog' }
]

// provide('navigation', navigation)
</script>

<template>
  <UApp>
    <NuxtLoadingIndicator />

    <!-- ถ้าเป็น public route ให้เรนเดอร์เลย
         ถ้าเป็น private และยังไม่ ready -> แสดง Loader -->
    <template v-if="!isPublicRoute && !auth.ready">
      <AuthLoading />
    </template>

    <template v-else>
      <NuxtLayout>
        <NuxtPage />
      </NuxtLayout>

      <!-- <ClientOnly>
        <LazyUContentSearch
          :files="files"
          shortcut="meta_k"
          :navigation="navigation"
          :links="links"
          :fuse="{ resultLimit: 42 }"
        />
      </ClientOnly> -->
    </template>
  </UApp>
</template>
