<!-- app/layouts/login.vue -->
<script setup lang="ts">
import { useColorMode } from "@vueuse/core"
import { useAuthStore } from "~/stores/auth"
import { useProfileStore } from "~/stores/profile"
import type { IntrospectResponse } from "~/types"

const colorMode = useColorMode()
const isDark = computed(() => colorMode.value === "dark")
const { $keycloak } = useNuxtApp()
const { t } = useI18n()
const toast = useToast()
const { setProfile, setPermissions } = useProfileStore()

// ✅ หลัง login ตรวจ introspect
const handleAfterLogin = async () => {
  try {
    const introspect = await useApi<IntrospectResponse>('/kapi/auth/introspect', {
      headers: { Authorization: `Bearer ${$keycloak.token ?? ''}` },
    })

    if (introspect?.status && introspect.detail) {
      const detail = introspect.detail
      const authStore = useAuthStore()

      authStore.setAuthenticated(true)
      authStore.setUser({
        id: detail.sub,
        username: detail.username,
        email: detail.email,
        firstName: detail.given_name,
        lastName: detail.family_name,
        districts: [],
        permissions: detail.permissions || [],
        role: detail.role,
        token: $keycloak.token ?? '',
        refreshToken: $keycloak.refreshToken ?? '',
        avatar: detail.avatar,
        locale: detail.locale || 'th',
      })

      setProfile({ ...detail })
      setPermissions(detail.permissions || [])

      if (useRoute().path !== '/dashboard') navigateTo('/dashboard')
    } else {
      toast.add({ title: 'Unauthorized', description: 'Your account is inactive or invalid.', color: 'error' })
    }
  } catch (error) {
    console.error('Post login process failed:', error)
  }
}

// ✅ manual login
const loginSSO = async () => {
  console.log('🔹 Trigger Keycloak Login')
  await $keycloak.login()
  if ($keycloak.authenticated) await handleAfterLogin()
}

// ✅ Auto login on mount
onMounted(async () => {
  if (!$keycloak.authenticated) await loginSSO()
  else await handleAfterLogin()
})

const logout = () => $keycloak.logout()
</script>


<template>
  <div class="min-h-screen flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-900">
    <slot />
  </div>
</template>
