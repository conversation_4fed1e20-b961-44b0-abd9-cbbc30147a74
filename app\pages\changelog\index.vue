<script setup lang="ts">
definePageMeta({
  layout: 'changelog',
  public: true
})
// const { data: meta } = await useAsyncData('changelog-meta', () =>
//   queryCollection('changelog' as const).first()
// )

// const { data: versions } = await useAsyncData('changelog-versions', () =>
//   queryCollection('versions' as const).order('date', 'DESC').all()
// )
</script>

<template>
  <!-- <div class="container mx-auto py-8">
    <h1 class="text-2xl font-bold">{{ meta?.title || 'Changelog' }}</h1>
    <p class="text-gray-500 mb-6">{{ meta?.description }}</p>

    <ul class="space-y-6">
    <li v-for="v in versions" :key="v.path">
        <h2>{{ v.title }}</h2>
        <ContentRenderer :value="v" />
    </li>
    </ul>
  </div> -->
</template>
