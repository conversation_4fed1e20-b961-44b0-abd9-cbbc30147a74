export interface UserWatch {
    id: string
    image: string
    prefix: string
    fname: string
    lname: string
    nickname: string
    age: string
    policeStation: string
    order: number
}

export interface UserWatchPolice {
    id: string
    photoUrl: string
    prefix: string
    fname: string
    lname: string
    nickname: string
    age: string
    order: number
    idcard: string
    titlename: string
    firstname: string
    lastname: string
    age: string
    sex: string
    birthday: string
    maritalStatus: string
    policeStation: string
    userRecorder: string
    personalType: string
    type: string
    crimesType: string
    passport: string
    fatherName: string
    fatherIdcard: string
    motherName: ustring
    motherIdcard: string
    deathStatus: string
    dateOfDeath: string
    policeRegion: string
    policeProvincial: string
    userPosition: string
    status: boolean
}