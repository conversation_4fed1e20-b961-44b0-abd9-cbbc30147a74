<template>
  <div class="alert-item">
    <div class="alert-header">
      <span class="alert-type">{{ alert.type }}</span>
      <!-- <span class="alert-time">{{ alert.time }}</span> -->
    </div>
    <p class="alert-details">
      วัตถุ: <span>{{ alert.details }}</span><br />
      วันที่: <span>{{ alert.time }}</span><br />
      กล้อง: <span>{{ alert.camera }}</span>
    </p>
  </div>
</template>

<script>
export default {
  name: 'AlertItem',
  props: {
    alert: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style scoped>
.alert-item {
  background-color: #f9f9f9;
  border-left: 5px solid #e74c3c; /* Red border for alerts */
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.alert-type {
  font-weight: bold;
  color: #e74c3c;
}

.alert-time {
  font-size: 12px;
  color: #888;
}

.alert-details {
  font-size: 13px;
  line-height: 1.4;
  text-align: left;
  color: #555;
  margin: 0;
}

.alert-details span {
  font-weight: bold;
}
</style>