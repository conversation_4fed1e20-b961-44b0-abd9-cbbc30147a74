<script setup lang="ts">
import { useColorMode } from "@vueuse/core"
import { useAuthStore } from '~/stores/auth'
import { useProfileStore } from '~/stores/profile'
import type { IntrospectResponse } from '~/types'
import { mapProfileToUserData } from '~/utils/authMapper'

const colorMode = useColorMode()
const isDark = computed(() => colorMode.value === "dark")
const { $keycloak } = useNuxtApp()

definePageMeta({ layout: "landing" })

const { t } = useI18n()
const toast = useToast()
const { setProfile, setPermissions } = useProfileStore()
const authStore = useAuthStore()

// ✅ หลัง login ตรวจ introspect
const handleAfterLogin = async () => {
  try {
    const introspect = await useApi<IntrospectResponse>('/kapi/auth/introspect', {
      headers: { Authorization: `Bearer ${$keycloak.token}` },
    })

    if (introspect?.status && introspect.detail) {
      const detail = introspect.detail
      const authStore = useAuthStore()

      authStore.setAuthenticated(true)
      authStore.setUser(mapProfileToUserData(detail, $keycloak.token ?? '', $keycloak.refreshToken ?? ''))

      setProfile({ ...detail })
      setPermissions(detail.permissions || [])

      if (useRoute().path !== '/police') {
        navigateTo('/police')
      }
    } else {
      toast.add({
        title: 'Unauthorized',
        description: 'Your account is inactive or invalid.',
        color: 'error',
      })
    }
  } catch (error) {
    console.error('Post login process failed:', error)
  }
}


// ✅ manual login
const loginSSO = async () => {
  console.log('🔹 Trigger Keycloak Login')
  await $keycloak.login()
  if ($keycloak.authenticated) {
    await handleAfterLogin()
  }
}

// ✅ Auto login on mount
onMounted(async () => {
  if (!$keycloak.authenticated) {
    console.log('🔹 Auto redirect to Keycloak...')
    await loginSSO()
  } else {
    console.log('🔹 Already authenticated')
    await handleAfterLogin()
  }
})

const logout = () => $keycloak.logout()
</script>

<template>
  <section class="text-center py-32 bg-gradient-to-b from-gray-50 to-white dark:from-gray-950 dark:to-gray-900">
    <h1 class="text-4xl font-bold mb-4">
      {{ t("landing.klynx.nextgen") }}
    </h1>
    <p class="text-gray-600 dark:text-gray-400 mb-6 max-w-xl mx-auto">
      {{ t("landing.klynx.description") }}
    </p>
    <UButton size="xl" @click="loginSSO">{{ t("landing.klynx.getstart") }}</UButton>
  </section>
</template>
