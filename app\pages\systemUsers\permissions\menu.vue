<!-- app/pages/systemUsers/permissions/index.vue -->

<script setup lang="ts">
import type { TableColumn, DropdownMenuItem } from '@nuxt/ui'
import { usePermissionTabs } from '~/composables/systemUsers/usePermissionTabs'
import { useClipboard } from '@vueuse/core'

interface Resource {
  _id?: string
  id: number
  name: string
  position?: string
  email?: string
  role?: string
  type?: string
  ownerUserId?: string
  editorUserIds?: string[]
  viewerUserIds?: string[]
  assignedRoleId?: string
  assignedGroupId?: string
  crud?: string[]
}

const tabs = usePermissionTabs()
const { t } = useI18n()
const { copy } = useClipboard()
const toast = useToast()

// ✅ Pagination & search
const page = ref(1)
const pageSize = ref(10)
const q = ref('')

// ✅ Loading & Error
const loading = ref(true)
const errorMsg = ref('')

// ✅ Data
const resources = ref<Resource[]>([])

// ✅ Columns สำหรับ UTable
const columns: TableColumn<Resource>[] = [
  { accessorKey: 'id', header: 'ID' },
  { accessorKey: 'name', header: 'Name' },
  { accessorKey: 'email', header: 'Email' },
  { accessorKey: 'role', header: 'Role' },
  { id: 'action', header: '' } // สำหรับ slot custom
]

// ✅ ฟังก์ชัน dropdown action
function getDropdownActions(user: Resource): DropdownMenuItem[][] {
  return [
    [
      {
        label: 'Copy resource Id',
        icon: 'i-lucide-copy',
        onSelect: () => {
          copy(user.id.toString())
          toast.add({
            title: 'Resource ID copied!',
            color: 'success',
            icon: 'i-lucide-circle-check'
          })
        }
      }
    ],
    [
      {
        label: 'Edit',
        icon: 'i-lucide-edit'
      },
      {
        label: 'Delete',
        icon: 'i-lucide-trash',
        color: 'error'
      }
    ]
  ]
}

// ✅ ดึงข้อมูล resource
const fetchResources = async () => {
  loading.value = true
  errorMsg.value = ''
  try {
    const resp = await useApi<any>('/kapi/resources', {
      params: { page: page.value, perPage: pageSize.value, q: q.value },
    })
    if (resp.data.value) {
      resources.value = resp.data.value.items || resp.data.value.resources || []
    } else {
      resources.value = []
    }
  } catch (err: any) {
    console.error(err)
    errorMsg.value = err?.message || 'Failed to fetch resources'
  } finally {
    loading.value = false
  }
}

// ✅ โหลดครั้งแรก
onMounted(fetchResources)

// ✅ Watch pagination & search → reload
watch([page, pageSize, q], () => {
  fetchResources()
})

// ✅ Computed group by type
const resourceGroups = computed(() => {
  const grouped: Record<string, Resource[]> = {}
  resources.value.forEach((res) => {
    const key = res.type || 'unknown'
    grouped[key] = grouped[key] || []
    grouped[key].push(res)
  })
  return grouped
})

// ✅ ฟังก์ชัน apply permission
const applyPermission = async (item: Resource) => {
  if (!item._id) {
    return useToast().add({
      title: 'Error',
      description: 'Resource ID is missing',
      color: 'error',
    })
  }

  const body = {
    resourceId: item._id,
    ownerUserId: item.ownerUserId,
    editorUserIds: item.editorUserIds,
    viewerUserIds: item.viewerUserIds,
    assignedRoleId: item.assignedRoleId,
    assignedGroupId: item.assignedGroupId,
    crud: item.crud,
    type: item.type,
  }

  await useApi('/kapi/authz/resource/grant', { method: 'POST', body })
  useToast().add({ title: 'Success', description: 'Permission applied!', color: 'success' })
  fetchResources() // รีเฟรชข้อมูล
}
</script>

<template>
  <UDashboardPanel id="permissions" :ui="{ body: 'lg:py-12' }">
    <template #header>
      <UDashboardNavbar :title="t('nav.permissions.main')">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>

      <UDashboardToolbar>
        <UNavigationMenu :items="tabs" highlight class="-mx-1 flex-1" />
        <UInput
          v-model="q"
          placeholder="Search resources..."
          icon="i-lucide-search"
          size="sm"
          class="ml-auto w-56"
        />
      </UDashboardToolbar>
    </template>

    <template #body>
      <div v-if="loading" class="p-8 text-center text-gray-500">
        Loading...
      </div>

      <div v-else-if="errorMsg" class="p-8 text-center text-red-500">
        {{ errorMsg }}
      </div>

      <div v-else class="space-y-8">
        <div
          v-for="(items, type) in resourceGroups"
          :key="type"
          class="space-y-4"
        >
          <h2 class="text-xl font-semibold capitalize">{{ type }} Resources</h2>
          <UTable :data="items" :columns="columns" class="flex-1">
            <template #name-cell="{ row }">
              <div class="flex items-center gap-3">
                <UAvatar
                  :src="`https://i.pravatar.cc/120?img=${row.original.id}`"
                  size="lg"
                  :alt="`${row.original.name} avatar`"
                />
                <div>
                  <p class="font-medium text-highlighted">{{ row.original.name }}</p>
                  <p>{{ row.original.position }}</p>
                </div>
              </div>
            </template>

            <template #action-cell="{ row }">
              <UDropdownMenu :items="getDropdownActions(row.original)">
                <UButton
                  icon="i-lucide-ellipsis-vertical"
                  color="neutral"
                  variant="ghost"
                  aria-label="Actions"
                />
              </UDropdownMenu>
            </template>
          </UTable>
        </div>

        <UPagination
          v-model="page"
          :page-count="Math.ceil(resources.length / pageSize)"
          :total="resources.length"
          class="justify-center mt-4"
        />
      </div>
    </template>
  </UDashboardPanel>
</template>
