<template>
  <header class="header">
    <div class="nav-controls">
      <!-- Date Picker -->
      <label>
        {{ $t("filter.dateStart") }}
        <input type="date" v-model="startDate" />
      </label>

      <label>
        {{ $t("filter.dateEnd") }}
        <input type="date" v-model="endDate" />
      </label>


      <!-- Button -->
      <button class="nav-button" @click="handleSetArea">
       {{ $t("filter.area") }} 
      </button>

      <!-- Select: ประเภทอุปกรณ์ -->
      <select v-model="selectedDeviceType">
        <option value=""> {{ $t("filter.deviceType") }}</option>
        <option v-for="type in deviceTypes" :key="type" :value="type">{{ type }}</option>
      </select>

      <!-- Select: อุปกรณ์ -->
      <select v-model="selectedDevice">
        <option value="">{{ $t("filter.device") }} </option>
        <option v-for="device in filteredDevices" :key="device" :value="device">{{ device }}</option>
      </select>
    </div>

    <div class="date-time">
      <span>{{ $t("filter.todayDate") }} : {{ todayDate }}</span>
      <span>{{ currentTime }}</span>
      <img src="/images/logo-dark.webp" alt="Flag" class="flag-icon" />
    </div>
  </header>
</template>

<script>
export default {
  name: 'TheHeader',
  data() {
    return {
      startDate: '',
      endDate: '',
      selectedDeviceType: '',
      selectedDevice: '',
      deviceTypes: ['CCTV', 'Sensor', 'Alarm'],
      devices: {
        CCTV: ['กล้อง 1', 'กล้อง 2'],
        Sensor: ['เซนเซอร์ A', 'เซนเซอร์ B'],
        Alarm: ['สัญญาณ 1', 'สัญญาณ 2'],
      },
      currentTime: '',
    };
  },
  computed: {
    todayDate() {
      const d = new Date();
      return d.toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' });
    },
    filteredDevices() {
      return this.selectedDeviceType ? this.devices[this.selectedDeviceType] : [];
    },
  },
  methods: {
    handleSetArea() {
      alert(`กำหนดพื้นที่สำหรับ: ${this.selectedDevice}`);
    },
    updateTime() {
      const now = new Date();
      this.currentTime = now.toLocaleTimeString('th-TH');
    },
  },
  mounted() {
    this.updateTime();
    setInterval(this.updateTime, 1000);
  },
};
</script>

<style scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: #333;
}

.nav-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.nav-button {
  padding: 8px 15px;
  border: none;
  border-radius: 5px;
  background-color: #9B0000;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
  transition: background-color 0.3s;
}

.nav-button:hover {
  background-color: #ccc;
}

.date-time {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.flag-icon {
  width: 24px;
  height: 18px;
}
</style>
