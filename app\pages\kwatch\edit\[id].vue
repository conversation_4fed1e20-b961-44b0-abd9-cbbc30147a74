<script setup lang="ts">
import * as z from 'zod'
import type { FormSubmitEvent } from '@nuxt/ui'
import { string } from 'zod/v4'
import { useAuthStore } from "~/stores/auth";

const authStore = useAuthStore();
console.log('authStore.user', authStore.user);

const route = useRoute()
const router = useRouter()
const { t } = useI18n()
const personId = route.params.id as string
const goBack = () => {
  router.push('/watchman')
}
const schema = z.object({
  personType: z.enum(['1', '2'], { required_error: 'Please select a person type' }),
  generalPersonType32: z.string().optional(),
  crimePersonType23: z.string().optional(),
  idType: z.enum(['idCard', 'passport'], { required_error: 'Please select an ID type' }),
  idNumber: z.string().min(13, 'ID Card number must be 13 digits').max(13, 'ID Card number must be 13 digits').optional(),
  prefix: z.string().min(1, 'Prefix is required'),
  firstName: z.string().min(2, 'First name is too short'),
  lastName: z.string().min(2, 'Last name is too short'),
  nickname: z.string().optional(),
  dateOfBirth: z.string().refine((val) => !isNaN(new Date(val).getTime()), { message: 'Invalid date' }),
  age: z.number().min(0, 'Age cannot be negative').max(150, 'Age seems too high').optional(),
  gender: z.string().min(1, 'Gender is required'),
  policeRegion: z.string().optional(),
  provincial: z.string().optional(),
  station: z.string().optional(),
  fatherName: z.string().optional(),
  fatherIdcard: z.string().optional(),
  motherName: z.string().optional(),
  motherIdcard: z.string().optional(),
  maritalStatus: z.string().optional(),
  deathStatus: z.string().optional(),
  dateOfDeath: z.string().optional(),
})

type Schema = z.output<typeof schema>

const state = reactive<Partial<Schema>>({
  personType: '1',
  generalPersonType32: undefined,
  crimePersonType23: undefined,
  idType: 'idCard',
  idNumber: undefined,
  prefix: undefined,
  firstName: undefined,
  lastName: undefined,
  nickname: undefined,
  dateOfBirth: undefined,
  age: undefined,
  gender: undefined,
  policeRegion: undefined,
  provincial: undefined,
  station: undefined,
  fatherName: undefined,
  fatherIdcard: undefined,
  motherName: undefined,
  motherIdcard: undefined,
  maritalStatus: undefined,
  deathStatus: undefined,
  dateOfDeath: undefined,
})

const open = ref(false)

const personTypeOptions = ref<{ label: string; value: any }[]>([])
const generalPersonType32Options = ref<{ label: string; value: any }[]>([])
const crimePersonType23Options = ref<{ label: string; value: any }[]>([])
const idTypeOptions = [
  { label: 'บัตรประจำประชาชน', value: 'idCard' },
  { label: 'ทะเบียนบ้าน/หนังสือเดินทางอื่นๆ', value: 'otherDoc' }
]
const genderOptions = ref<{ label: string; value: any }[]>([])
const prefixOptions = ref<{ label: string; value: any }[]>([])

const policeRegionOption = ref<{ label: string; value: any }[]>([])
const policeProvincialOptions = ref<{ label: string; value: any }[]>([])
const policeStationOptions = ref<{ label: string; value: any }[]>([])

const maritalStatusOptions = ref<{ label: string; value: any }[]>([])
const deathStatusOptions = ref<{ label: string; value: any }[]>([])

const toast = useToast()

const fetchOption = async () => {
  try {
    const resp = await useApiWatchMan<{
      listOptions: {
        type: { id: number; title: string }[],
        personType: { id: number; title: string }[],
        crimesType: { id: number; title: string }[],
        sex: { id: number; title: string }[],
        titlename: { id: number; title: string }[],
        policeRegion: { id: number; title: string }[],
        maritalStatus: { id: number; title: string }[],
        deathStatus: { id: number; title: string }[],
      }
    }>('/WatchmanData/api_options.php')

    const apiPersonTypeOptions = resp.listOptions.type.map(item => ({
      label: item.title,
      value: item.id.toString(),
    }))
    personTypeOptions.value = [...apiPersonTypeOptions]

    const apiGeneralPersonType32Options = resp.listOptions.personType.map(item => ({
      label: item.title,
      value: item.id.toString(),
    }))
    generalPersonType32Options.value = [...apiGeneralPersonType32Options]

    const apiCrimePersonType23Options = resp.listOptions.crimesType.map(item => ({
      label: item.title,
      value: item.id.toString(),
    }))
    crimePersonType23Options.value = [...apiCrimePersonType23Options]

    const apiGenderOptions = resp.listOptions.sex.map(item => ({
      label: item.title,
      value: item.id.toString(),
    }))
    genderOptions.value = [...apiGenderOptions]

    const apiPrefixOptions = resp.listOptions.titlename.map(item => ({
      label: item.title,
      value: item.id.toString(),
    }))
    prefixOptions.value = [...apiPrefixOptions]

    const apiPoliceRegion = resp.listOptions.policeRegion.map(item => ({
      label: item.title,
      value: item.id.toString(),
    }))
    policeRegionOption.value = [...apiPoliceRegion]

    const apiMaritalStatusOptions = resp.listOptions.maritalStatus.map(item => ({
      label: item.title,
      value: item.id.toString(),
    }))
    maritalStatusOptions.value = [...apiMaritalStatusOptions]

    const apiDeathStatusOptions = resp.listOptions.deathStatus.map(item => ({
      label: item.title,
      value: item.id.toString(),
    }))
    deathStatusOptions.value = [...apiDeathStatusOptions]

  } catch (err) {
    console.error('Error fetching users:', err)
  }
}

onMounted(async () => {
  await fetchOption()
  await fetchPersonData()
})


async function onSubmit(event: FormSubmitEvent<Schema>) {
  toast.add({
    title: 'Success',
    description: `Edit WatchMan ${event.data.firstName} ${event.data.lastName} Edit`,
    color: 'success'
  })
  console.log('Form Data:', event.data)

  const formData = new FormData()

  // แนบข้อมูลจาก event.data
  formData.append('id', personId)
  formData.append('type', event.data.personType || '')
  formData.append('personalType', event.data.generalPersonType32 || '')
  formData.append('crimesType', event.data.crimePersonType23 || '')
  formData.append('idcard', event.data.idType == 'idCard' ? event.data.idNumber || '' : '')
  formData.append('passport', event.data.idType == 'passport' ? event.data.idNumber || '' : '')
  formData.append('titlename', event.data.prefix || '')
  formData.append('subTitlename', '')
  formData.append('firstname', event.data.firstName || '')
  formData.append('lastname', event.data.lastName || '')
  formData.append('nickname', event.data.nickname || '')
  formData.append('sex', event.data.gender || '')
  formData.append('birthday', event.data.dateOfBirth || '')
  formData.append('age', event.data.age?.toString() || '')
  formData.append('fatherName', event.data.fatherName || '')
  formData.append('fatherIdcard', event.data.fatherIdcard || '')
  formData.append('motherName', event.data.motherName || '')
  formData.append('motherIdcard', event.data.motherIdcard || '')
  formData.append('maritalStatus', event.data.maritalStatus || '')
  formData.append('deathStatus', event.data.deathStatus || '')
  formData.append('dateOfDeath', event.data.dateOfDeath || '')
  formData.append('policeRegion', event.data.policeRegion || '')
  formData.append('policeProvincial', event.data.provincial || '')
  formData.append('policeStation', event.data.station || '')

  // แนบไฟล์รูปภาพ
  if (selectedFile.value) {
    formData.append('photo', selectedFile.value)
  }

  for (const [key, val] of formData.entries()) {
    console.log(`${key}:`, val)
  }


  // ผู้บันทึก
  if (authStore.user) {
    formData.append('userRecorder', `${authStore.user.firstName} ${authStore.user.lastName}`);
    formData.append('userPosition', authStore.user.role);
  } else {
    formData.append('userRecorder', `ทดสอบ ทดสอบ`);
    formData.append('userPosition', 'ทดสอบ');
  }


  try {
    const response = await useApiWatchMan('/WatchmanData/api_update_person.php', {
      method: 'POST',
      body: formData
    })

    // const result = await response.json()
    const result = response
    // console.log('ผลลัพธ์:', result)
    // open.value = false
    router.push('/watchman')
  } catch (err) {
    console.error('Upload failed:', err)
  }
}


// โหลด provincial ตาม regionId
const fetchProvincial = async (regionId: string) => {
  try {
    const resp = await useApiWatchMan<{ listPoliceProvincial: { id: number; title: string }[] }>(
      `/WatchmanData/api_options.php?f=provincial&regionId=${regionId}`
    )
    policeProvincialOptions.value = resp.listPoliceProvincial.map(item => ({
      label: item.title,
      value: item.id.toString(),
    }))
  } catch (err) {
    console.error('Error fetching provincial:', err)
  }
}

// โหลด station ตาม provincialId
const fetchStation = async (provincialId: string) => {
  try {
    const resp = await useApiWatchMan<{ listPoliceStation: { id: number; title: string }[] }>(
      `/WatchmanData/api_options.php?f=station&provincialId=${provincialId}`
    )
    policeStationOptions.value = resp.listPoliceStation.map(item => ({
      label: item.title,
      value: item.id.toString(),
    }))
  } catch (err) {
    console.error('Error fetching station:', err)
  }
}
const isLoadingInitialData = ref(true)

watch(() => state.policeRegion, (regionId) => {
  if (!isLoadingInitialData.value && regionId) {
    state.provincial = undefined
    state.station = undefined
    fetchProvincial(regionId)
  }
})

watch(() => state.provincial, (provincialId) => {
  if (!isLoadingInitialData.value && provincialId) {
    state.station = undefined
    fetchStation(provincialId)
  }
})


const fileInput = ref<HTMLInputElement | null>(null)
const selectedFile = ref<File | null>(null)
const imagePreviewUrl = ref<string | null>(null)

const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    selectedFile.value = file
    imagePreviewUrl.value = URL.createObjectURL(file)
  }
}

const calculateAge = (birthdate: string): number => {
  const today = new Date()
  const birthDate = new Date(birthdate)

  let age = today.getFullYear() - birthDate.getFullYear()
  const m = today.getMonth() - birthDate.getMonth()

  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }

  return age
}


const fetchPersonData = async () => {
  try {
    const response = await useApiWatchMan<{ details: any; status: boolean }>(
      `/WatchmanData/api_get_person.php?id=${personId}`
    )

    const data = response.details

    // ใส่ค่าให้กับ state
    state.personType = data.typeCode.toString()
    state.generalPersonType32 = data.typeCode === 1 ? data.personTypeCode : undefined
    state.crimePersonType23 = data.typeCode === 2 ? data.crimesTypeCode : undefined

    state.idType = data.passport ? 'passport' : 'idCard'
    state.idNumber = data.passport || data.idcard || ''
    state.prefix = data.titlename
    state.firstName = data.firstname
    state.lastName = data.lastname
    state.nickname = data.nickname
    state.dateOfBirth = data.birthday
    state.age = calculateAge(data.birthday)
    state.gender = data.sex

    state.fatherName = data.fathername
    state.fatherIdcard = data.fatherIdcard
    state.motherName = data.mothername
    state.motherIdcard = data.motherIdcard
    state.maritalStatus = data.maritalStatus
    state.deathStatus = String(data.deathCode)
    state.dateOfDeath = data.deathDate

    // โหลดภาพ (ถ้ามี)
    if (data.imagePath) {
      imagePreviewUrl.value = data.imagePath
    }

    // โหลด provincial/station ตามโค้ด
    if (data.policeRegionCode) {
      await fetchProvincial(data.policeRegionCode)
    }
    if (data.policeProvincialCode) {
      await fetchStation(data.policeProvincialCode)
    }

    state.policeRegion = data.policeRegionCode.toString()
    state.provincial = data.policeProvincialCode.toString()
    state.station = data.policeStationCode.toString()

  } catch (err) {
    console.error('Error loading person data:', err)
  }
}

watch(() => state.dateOfBirth, (newDate) => {
  if (!newDate) {
    state.age = null
    return
  }

  const birthDate = new Date(newDate)
  const today = new Date()

  let age = today.getFullYear() - birthDate.getFullYear()
  const monthDiff = today.getMonth() - birthDate.getMonth()
  const dayDiff = today.getDate() - birthDate.getDate()

  if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
    age--
  }

  state.age = age
})
</script>

<template>
  <UDashboardPanel id="watchman">
    <template #header>
      <UDashboardNavbar :title="t('Watchman.title')">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <div class="date-time">
            <svg width="27" height="26" viewBox="0 0 27 26" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M25.9943 22.9648C25.1639 22.2246 24.4369 21.3759 23.8329 20.4418C23.1729 19.1525 22.7776 17.7442 22.6704 16.2998V12.0459C22.6761 9.77742 21.8532 7.58492 20.3564 5.88035C18.8595 4.17578 16.7917 3.07647 14.5415 2.78898V1.67815C14.5415 1.37326 14.4204 1.08086 14.2048 0.86527C13.9892 0.649681 13.6968 0.528564 13.3919 0.528564C13.0871 0.528564 12.7947 0.649681 12.5791 0.86527C12.3635 1.08086 12.2424 1.37326 12.2424 1.67815V2.8062C10.0123 3.11442 7.96951 4.22037 6.49232 5.91921C5.01513 7.61806 4.20366 9.79467 4.2082 12.0459V16.2998C4.10104 17.7442 3.70577 19.1525 3.0457 20.4418C2.45229 21.3738 1.73698 22.2223 0.918754 22.9648C0.826901 23.0455 0.753284 23.1448 0.702803 23.2562C0.652321 23.3676 0.626131 23.4884 0.625977 23.6106V24.7818C0.625977 25.0101 0.7167 25.2292 0.87819 25.3907C1.03968 25.5521 1.25871 25.6429 1.48709 25.6429H25.426C25.6544 25.6429 25.8734 25.5521 26.0349 25.3907C26.1964 25.2292 26.2871 25.0101 26.2871 24.7818V23.6106C26.2869 23.4884 26.2607 23.3676 26.2103 23.2562C26.1598 23.1448 26.0862 23.0455 25.9943 22.9648ZM2.41709 23.9206C3.21809 23.1465 3.92348 22.2792 4.5182 21.3373C5.34985 19.7804 5.83456 18.0619 5.93903 16.2998V12.0459C5.90488 11.0367 6.07416 10.031 6.43679 9.08858C6.79942 8.14618 7.34799 7.28638 8.04983 6.56039C8.75166 5.8344 9.59242 5.25707 10.522 4.86277C11.4516 4.46848 12.4511 4.26528 13.4608 4.26528C14.4706 4.26528 15.4701 4.46848 16.3997 4.86277C17.3293 5.25707 18.17 5.8344 18.8718 6.56039C19.5737 7.28638 20.1222 8.14618 20.4849 9.08858C20.8475 10.031 21.0168 11.0367 20.9826 12.0459V16.2998C21.0871 18.0619 21.5718 19.7804 22.4035 21.3373C22.9982 22.2792 23.7036 23.1465 24.5046 23.9206H2.41709Z"
                fill="#9B0000" />
            </svg>
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M26.1998 5C23.4998 2.5 19.9498 1 15.9998 1C12.0498 1 8.4998 2.5 5.7998 5H26.1998ZM5.7998 27C8.4998 29.5 12.0498 31 15.9998 31C19.9498 31 23.4998 29.5 26.1998 27H5.7998Z"
                fill="#ED4C5C" />
              <path
                d="M1 16C1 18.15 1.45 20.15 2.25 22H29.75C30.55 20.15 31 18.15 31 16C31 13.85 30.55 11.85 29.75 10H2.25C1.45 11.85 1 13.85 1 16Z"
                fill="#2A5F9E" />
              <path
                d="M5.8002 27H26.1502C27.6502 25.6 28.9002 23.9 29.7002 22H2.2002C3.1002 23.9 4.3002 25.6 5.8002 27ZM26.2002 5H5.8002C4.3002 6.4 3.0502 8.1 2.2502 10H29.7502C28.9002 8.1 27.7002 6.4 26.2002 5Z"
                fill="#F9F9F9" />
            </svg>

          </div>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="h-screen overflow-y-auto p-6 bg-white" style="width: 100%; padding: 0px;">
        <UForm :schema="schema" :state="state" class="space-y-6" @submit="onSubmit">
          <div class="rounded-lg shadow-sm" style="display: flex;justify-content: space-around;">
            <div style="flex: 1; max-width: 50%; margin-right: 20px;">
              <h3 class="text-lg font-semibold mb-4">กรอกข้อมูลบุคคล</h3>
              <p class="text-sm text-gray-500 mb-4">กรุณากรอกข้อมูลให้ครบถ้วนเพื่อความสะดวกในการติดต่อ</p>

              <div class="flex flex-col space-y-4">
                <UFormField label="ลำดับ ประเภทบุคคล" name="personType">
                  <URadioGroup v-model="state.personType" :items="personTypeOptions" orientation="horizontal" />
                </UFormField>

                <!-- แสดงถ้าเลือก บุคคลทั่วไป -->
                <UFormField v-if="state.personType == '1'" label="บุคคลทั่วไป 32 ประเภท" name="generalPersonType32">
                  <USelect v-model="state.generalPersonType32" :items="generalPersonType32Options" placeholder="เลือก"
                    class="w-full" />
                </UFormField>

                <!-- แสดงถ้าเลือก บุคคลเกี่ยวข้องอาชญากรรม -->
                <UFormField v-if="state.personType == '2'" label="บุคคลอาชญากรรม 23 ประเภท" name="crimePersonType23">
                  <USelect v-model="state.crimePersonType23" :items="crimePersonType23Options" placeholder="เลือก"
                    class="w-full" />
                </UFormField>
                <!-- Image Upload Area -->
                <UFormField label="รูปภาพหน้าของบุคคล" name="personImage">
                  <div
                    class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center flex flex-col items-center justify-center h-60 space-y-2">
                    <!-- ปุ่มเลือกไฟล์ -->
                    <UButton icon="i-lucide-plus" label="เพิ่มรูปภาพ" variant="outline" @click="triggerFileInput" />

                    <!-- input ซ่อน -->
                    <input ref="fileInput" type="file" accept="image/*" class="hidden" @change="handleFileChange" />

                    <!-- แสดงรูปภาพที่เลือก -->
                    <img v-if="imagePreviewUrl" :src="imagePreviewUrl" alt="ภาพที่เลือก"
                      class="mt-4 w-32 h-32 object-cover rounded-md border" />
                  </div>
                </UFormField>



                <UFormField name="idType">
                  <URadioGroup v-model="state.idType" :items="idTypeOptions" orientation="horizontal" />
                </UFormField>

                <UFormField label="เลขบัตรประชาชน" placeholder="กรอกเลขบัตรประจำประชาชน 13 หลัก เฉพาะตัวเลขเท่านั้น"
                  name="idNumber">
                  <UInput v-model="state.idNumber" type="text" inputmode="numeric" pattern="[0-9]*" maxlength="13"
                    class="w-full" />
                </UFormField>

                <div class="grid grid-cols-2 gap-4">
                  <UFormField label="เพศ" name="gender">
                    <USelect v-model="state.gender" :items="genderOptions" placeholder="เลือก" class="w-full" />
                  </UFormField>
                  <UFormField label="คำนำหน้า" name="prefix">
                    <USelect v-model="state.prefix" :items="prefixOptions" placeholder="เลือก" class="w-full" />
                  </UFormField>
                </div>

                <div class="grid grid-cols-2 gap-4">
                  <UFormField label="ชื่อ" placeholder="ชื่อ" name="firstName">
                    <UInput v-model="state.firstName" class="w-full" />
                  </UFormField>
                  <UFormField label="นามสกุล" placeholder="นามสกุล" name="lastName">
                    <UInput v-model="state.lastName" class="w-full" />
                  </UFormField>
                </div>

                <div class="grid grid-cols-2 gap-4">
                  <UFormField label="ชื่อเล่น/ฉายา" placeholder="ชื่อเล่น/ฉายา" name="nickname">
                    <UInput v-model="state.nickname" class="w-full" />
                  </UFormField>
                  <UFormField label="วันเดือนปีเกิด" name="dateOfBirth">
                    <UInput v-model="state.dateOfBirth" type="date" class="w-full" />
                  </UFormField>
                </div>

                <UFormField label="อายุ" name="age">
                  <UInput v-model.number="state.age" type="number" class="w-full" />
                </UFormField>
              </div>
            </div>

            <div style="flex: 1; max-width: 50%;">
              <!-- Right Column -->
              <div class="flex flex-col space-y-4">
                <div class="grid grid-cols-2 gap-4">
                  <UFormField label="ชื่อ - นามสกุล บิดา" name="fatherName">
                    <UInput v-model="state.fatherName" class="w-full" />
                  </UFormField>
                  <UFormField label="เลขที่บัตรประชาชน บิดา" name="fatherIdcard">
                    <UInput v-model="state.fatherIdcard" type="text" inputmode="numeric" pattern="[0-9]*" maxlength="13"
                      class="w-full" />
                  </UFormField>
                </div>

                <div class="grid grid-cols-2 gap-4">
                  <UFormField label="ชื่อ - นามสกุล มารดา" name="motherName">
                    <UInput v-model="state.motherName" class="w-full" />
                  </UFormField>
                  <UFormField label="เลขที่บัตรประชาชน มารดา" name="motherIdcard">
                    <UInput v-model="state.motherIdcard" type="text" inputmode="numeric" pattern="[0-9]*" maxlength="13"
                      class="w-full" />
                  </UFormField>
                </div>

                <div class="grid grid-cols-2 gap-4">
                  <UFormField label="สถานภาพสมรส" name="maritalStatus">
                    <USelect v-model="state.maritalStatus" :items="maritalStatusOptions" placeholder="เลือก"
                      class="w-full" />
                  </UFormField>

                  <UFormField label="สถานะการมีชีวิตอยู่" name="deathStatus">
                    <URadioGroup v-model="state.deathStatus" :items="deathStatusOptions" orientation="horizontal" />
                  </UFormField>

                  <!-- แสดงเฉพาะเมื่อเลือก "เสียชีวิต" -->
                  <UFormField v-if="Number(state.deathStatus) === 2" label="วันเดือนปีที่เสียชีวิต" name="dateOfDeath">
                    <UInput v-model="state.dateOfDeath" type="date" class="w-full" />
                  </UFormField>
                </div>

                <!-- Section: สถานที่ติดต่อ (Contact Information) -->
                <div class="p-4 rounded-lg">
                  <h3 class="text-lg font-semibold mb-4">สถานที่ติดต่อ (รับผิดชอบสถานที่ ที่พักอาศัย)</h3>
                  <p class="text-sm text-gray-500 mb-4">กรุณากรอกข้อมูลให้ครบถ้วนเพื่อความสะดวกในการติดต่อ</p>

                  <UFormField label="บช." name="policeRegion">
                    <USelect v-model="state.policeRegion" :items="policeRegionOption" placeholder="เลือก"
                      class="w-full" />
                  </UFormField>
                  <UFormField label="บก." name="provincial">
                    <USelect v-model="state.provincial" :items="policeProvincialOptions" placeholder="เลือก"
                      class="w-full" />
                  </UFormField>
                  <UFormField label="สป./สก." name="station">
                    <USelect v-model="state.station" :items="policeStationOptions" placeholder="เลือก" class="w-full" />
                  </UFormField>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Action Buttons -->
          <div class="flex justify-end gap-2 mt-6">
            <UButton label="Cancel" color="neutral" variant="subtle" @click="goBack" />
            <UButton label="บันทึกข้อมูล" color="primary" variant="solid" type="submit" icon="i-lucide-save" />
          </div>
        </UForm>
      </div>
    </template>
  </UDashboardPanel>
</template>

<style scoped>
.date-time {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

</style>
