<template>
  <div class="detected-face-item flex flex-col justify-center items-center">
    <img :src="face.image" alt="Detected Face" class="face-image" />
    <span class="timestamp">{{ face.timestamp }}</span>
  </div>
</template>


<script>
export default {
  name: 'DetectedFaceItem',
  props: {
    face: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style scoped>
.detected-face-item {
  display: flex;
  flex-direction: column; /* สำคัญ */
  align-items: center;
  text-align: center;
  background-color: #f0f2f5;
  padding: 5px;
  border-radius: 5px;
}


.face-image {
  width: 60%;
  height: 90px; /* Adjust as needed */
  object-fit: cover;
  border-radius: 3px;
  margin-bottom: 5px;
}

.timestamp {
  font-size: 12px;
  color: #666;
}
</style>