// app/plugins/initAuth.client.ts
import { useAuthStore } from '~/stores/auth'
import { useProcessAuthCallback } from '~/composables/useProcessAuthCallback'

export default defineNuxtPlugin(async () => {
    const auth = useAuthStore()
    auth.ready = false

    const run = useProcessAuthCallback()
    const processed = await run()

    if (!processed) {
        try {
            const token = localStorage.getItem('auth:token')
            const refresh = localStorage.getItem('auth:refresh')
            const userStr = localStorage.getItem('auth:user')
            if (token && userStr) {
                ; (auth as any).refreshToken = refresh || ''
                auth.user = JSON.parse(userStr)
                auth.isAuthenticated = true
            }
        } catch { }
    }

    auth.ready = true
})
