<script setup lang="ts">
// import type { ContentNavigationItem } from '@nuxt/content'
// import AppHeader from '~/components/saas/AppHeader.vue'
// import AppFooter from '~/components/saas/AppFooter.vue'
const { t } = useI18n()
// const navigation = inject<Ref<ContentNavigationItem[]>>('navigation')
</script>

<template>
  <div>
    <!-- <AppHeader /> -->
    <SaasAppHeader />
    <UMain>
      <UContainer>
        <UPage>
          <template #left>
            <UPageAside>
              <template #top>
                <!-- <UContentSearchButton :collapsed="false" /> -->
              </template>

              <!-- <UContentNavigation
                :navigation="navigation"
                highlight
              /> -->
            </UPageAside>
          </template>

          <slot />
        </UPage>
      </UContainer>
    </UMain>
    <footer class="py-8 text-center text-gray-500 dark:text-gray-400 text-sm">
      {{ t("landing.klynx.copyright") }}
    </footer>
    <!-- <AppFooter /> -->
  </div>
</template>