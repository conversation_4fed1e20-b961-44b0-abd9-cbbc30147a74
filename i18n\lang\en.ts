export default {
  global: {
    title: 'Klynx Platform',
    description: 'Intelligent operations center',
    copyright: 'Copyright © 2025 Point IT Consulting Co. Ltd',
    headersBrand: 'Klynx Platform',
    loading: 'Login checking...'
  },
  landing: {
    klynx: {
      main: "Klynx Platform",
      login: "Login",
      copyright: "Copyright © 2025 Point IT Consulting Co. Ltd",
      nextgen: "Next-gen IOC Platform",
      description: "Build and manage your resources with Klynx IOC. Secure, scalable, and fast.",
      getstart: "Get Started"
    },
    home: {
      main: "Home"
    },
    docs: {
      main: "Docs"
    },
    pricing: {
      main: "Pricing"
    },
    changelog: {
      main: "Changelog"
    }
  },
  nav: {
    approve: 'Approval',
    report: 'Report',
    setting: 'Setting',
    profile: 'Profile',
    logout: 'Logout',
    login: 'Login',
    svms: 'K-Super vms',
    iboc: 'AI Platform',
    main: 'Home',
    myBooking: 'My Bookings',
    api: {
      main: 'API',
    },
    menu: {
      main: 'Menu',
    },
    permissions: {
      main: 'Permissions',
      all: 'All Permissions',
      create: 'Create Permission',
      edit: 'Edit Permission',
      delete: 'Delete Permission',
      menu: 'Menu Access',
      api: 'API Access',
      resource: 'Resource Access'
    },
    users: {
      title: 'Users management',
      main: 'Users',
      all: 'Member List',
      approvel: 'Approval Groups',
      role: 'Roles',
      organizations: 'Organizations'
    },
    watchman: {
      main: 'Watchman Systems',
      dashboard: 'Dashboard',
      events: 'Events'
    },
  },
  navSetting: {
    title: 'System Settings',
    theme: 'Theme',
    appearance: 'Appearance',
    light: 'Light',
    dark: 'Dark',
    site: 'Site Setting',
    organization: 'Organization Setting',
    website: 'Website Setting',
    login: 'Login page',
    email: 'Email Setting',
    notifications: 'Notification Setting',
    line: 'Line Setting',
    telegram: 'Telegram Setting',
    api: 'API Setting',
    sms: 'SMS Setting',
    module: 'Module',
    language: 'Language',
    th: 'Thai',
    en: 'English',
    department: 'Department',
    cookie: 'Cookie Policy',
    history: 'Usage History',
    devices: {
      title: 'Device Management',
      cameras: 'cameras',
      kControls: 'kControls',
      speakers: 'Speakers',
      aiTracker: 'AI Tracker'
    },
    vehicle: {
      title: 'Vehicle Management',
      settings: 'Settings',
      list: 'List of vehicles',
      accessories: 'Accessories',
      types: 'Vehicle Types',
      brand: 'Vehicle Brands'
    }
  },
  colors: {
    primary: 'Primary',
    neutral: 'Neutral',
    red: 'Red',
    orange: 'Orange',
    amber: 'Amber',
    yellow: 'Yellow',
    lime: 'Lime',
    green: 'Green',
    emerald: 'Emerald',
    teal: 'Teal',
    cyan: 'Cyan',
    sky: 'Sky',
    blue: 'Blue',
    indigo: 'Indigo',
    violet: 'Violet',
    purple: 'Purple',
    fuchsia: 'Fuchsia',
    pink: 'Pink',
    rose: 'Rose',
    slate: 'Slate',
    gray: 'Gray',
    zinc: 'Zinc',
    stone: 'Stone'
  },
  systemUsers: {
    title: "System Users",
    table: {
      username: 'Username',
      fullname: 'Full Name',
      email: 'Email',
      phone: 'Phone',
      role: 'Role',
      status: 'Status',
      district: 'District',
      location: 'Location',
      createdAt: 'Created At',
      order: 'Order',
      actions: 'Actions'
    },
    status: {
      active: 'Active',
      inactive: 'Inactive'
    },

    searchPlaceholder: "Search users...",
    pagination: {
      showing: "Showing",
      of: "of",
      users: "users"
    },
    form: {
      titleCreate: 'Create User',
      titleEdit: 'Edit User',
      username: 'Username',
      fullname: 'Full Name',
      email: 'Email',
      phone: 'Phone',
      role: 'Role',
      district: 'District',
      location: 'Location',
      status: 'Status',
      submit: 'Save',
      cancel: 'Cancel'
    },
    messages: {
      created: '✅ User has been created successfully',
      updated: '✅ User has been updated successfully',
      deleted: '🗑 User has been deleted',
      confirmDelete: 'Are you sure you want to delete this user?'
    }
  },
  dashboard: {
    title: 'Dashboard',
    stats: {
      pending: 'Pending Bookings',
      approved: 'Approved Bookings',
      rejected: 'Rejected Bookings',
      vehicle: 'Total Vehicles'
    }
  },
  watchman: {
    title: 'Watchman"',
    table: {
      "order": 'Order',
      "image": 'Image',
      "prefix": 'Prefix',
      "fname": 'First name',
      "lname": 'Last name',
      "nickname": 'Nickname',
      "age": 'Age',
      "policeStation": 'PoliceStation',
      "action": 'Action',
    }
  },
  kwatch: {
    main: "kwatch",
  },
  police: {
    title: 'kwatch',
    table: {
      "order": 'Order',
      "image": 'Image',
      "prefix": 'Prefix',
      "fname": 'First name',
      "lname": 'Last name',
      "nickname": 'Nickname',
      "age": 'Age',
      "policeStation": 'PoliceStation',
      "action": 'Action',
    }
  }
}
