// app/composables/system-users/usePermissionTabs.ts
import type { NavigationMenuItem } from '@nuxt/ui'
import { useI18n } from 'vue-i18n'

export function usePermissionTabs() {
    const { t } = useI18n()

    const tabs = [[
        { label: t('nav.permissions.menu'), icon: 'i-lucide-menu', to: '/systemUsers/permissions/menu' },
        { label: t('nav.permissions.api'), icon: 'i-lucide-server', to: '/systemUsers/permissions/api' },
        { label: t('nav.permissions.resource'), icon: 'i-lucide-box', to: '/systemUsers/permissions' }
    ]] satisfies NavigationMenuItem[][]

    return tabs
}
