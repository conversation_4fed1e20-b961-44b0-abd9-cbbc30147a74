<!-- app/components/saas/LogoBrand.vue -->
<script setup lang="ts">
import { ref, onMounted } from 'vue'
const { t } = useI18n()
const loaded = ref(false)
const logoUrl = '/images/logo.webp' // public path
onMounted(() => {
  const img = new Image()
  img.src = logoUrl
  img.onload = () => (loaded.value = true)
})
</script>

<!-- app/components/saas/LogoBrand.vue -->
<template>
  <div class="flex items-center gap-2">
    <!-- Light mode -->
    <NuxtImg
      src="/images/logo.webp"
      width="38"
      height="38"
      class="rounded-full dark:hidden"
      alt="Klynx logo"
      loading="eager"
    />

    <!-- Dark mode -->
    <NuxtImg
      src="/images/logo-dark.webp"
      width="38"
      height="38"
      class="rounded-full hidden dark:block"
      alt="Klynx logo dark"
      loading="eager"
    />

    <span class="font-bold text-xl">Klynx Platform</span>
  </div>
</template>

