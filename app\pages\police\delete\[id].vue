<script setup lang="ts">
import { useApi, useApiWatchMan } from '~/composables/useApi'

const route = useRoute()
const router = useRouter()
const toast = useToast()

const userId = computed(() => route.params.id as string)
const loading = ref(false)
const deleting = ref(false)

// Fetch user data for confirmation
const user = ref<any>(null)

const fetchUser = async () => {
  try {
    loading.value = true
    const resp = await useApi<{ status: boolean; detail: any }>(
      `/api/v1/kwatch/watchlist/${userId.value}` // ส่ง id ใน URL
    )

    if (resp.status && resp.detail) {
      user.value = resp.detail
    } else {
      user.value = null
    }
  } catch (err) {
    console.error('Error fetching user:', err)
    toast.add({
      title: 'Error',
      description: 'Failed to fetch user data',
      color: 'red',
    })
  } finally {
    loading.value = false
  }
}


// Delete user
const deleteUser = async () => {
  if (!userId.value) return
  try {
    deleting.value = true
    const resp = await useApi<{ status: boolean; message?: string }>(
      `/api/v1/kwatch/watchlist/${userId.value}`,
      { method: 'DELETE' }
    )

    if (resp.status) {
      toast.add({ title: 'Success', description: 'User deleted successfully', color: 'green' })
      // กลับไปหน้า list พร้อม query เพื่อ reload
      router.push({ path: '/police', query: { reload: Date.now().toString() } })
    } else {
      toast.add({ title: 'Error', description: resp.message || 'Failed to delete user', color: 'red' })
    }
  } catch (err) {
    console.error('Error deleting user:', err)
    toast.add({ title: 'Error', description: 'Failed to delete user', color: 'red' })
  } finally {
    deleting.value = false
  }
}

onMounted(fetchUser)
</script>

<template>
  <UDashboardPanel>
    <UDashboardNavbar title="Delete Watchman">
      <template #right>
        <UButton label="Cancel" color="gray" variant="ghost" @click="router.push('/police')" />
      </template>
    </UDashboardNavbar>

    <UDashboardPanelContent>
      <div v-if="loading" class="flex justify-center items-center h-64">
        <USpinner size="lg" />
      </div>

      <div v-else-if="user" class="max-w-2xl mx-auto bg-white p-6 rounded-lg shadow border border-gray-200">
        <div class="text-center mb-8">
          <UIcon name="i-heroicons-exclamation-triangle" class="w-12 h-12 text-red-500 mx-auto" />
          <h2 class="text-2xl font-bold mt-4">Confirm Deletion</h2>
          <p class="text-gray-600 mt-2">
            Are you sure you want to delete this watchman? This action cannot be undone.
          </p>
        </div>

        <div class="border-t border-b border-gray-200 py-6 mb-6">
          <div class="flex items-center space-x-4">
            <img :src="user.imagePath || '/images/logo-dark.webp'" alt="Profile"
              class="w-16 h-16 rounded-full object-cover border-2 border-gray-200" />
            <div>
              <h3 class="font-semibold text-lg">{{ user.titlename }} {{ user.firstname }} {{ user.lastname }}</h3>
              <p class="text-gray-600">{{ user.idcard }}</p>
              <p class="text-gray-600">{{ user.policeStationDetail }}</p>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3">
          <UButton label="Cancel" color="gray" variant="outline" @click="router.push('/police')" />
          <UButton label="Delete Permanently" color="red" :loading="deleting" @click="deleteUser" />
        </div>
      </div>

      <div v-else class="text-center py-12">
        <p class="text-gray-500">User not found</p>
        <UButton label="Back to List" color="gray" variant="ghost" class="mt-4" @click="router.push('/police')" />
      </div>
    </UDashboardPanelContent>
  </UDashboardPanel>
</template>