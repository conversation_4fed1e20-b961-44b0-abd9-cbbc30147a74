// app/plugins/keycloak.client.ts
import { defineNuxtPlugin, useRuntimeConfig, navigateTo } from '#app'
import Keycloak from 'keycloak-js'
import { useAuthStore } from '~/stores/auth'
import { useStateStore } from '~/stores/auth/state'
import type { UserData, IntrospectResponse } from '~/types'

/** Decode JWT payload safely */
function parseJwt(token?: string) {
  if (!token) return null
  const parts = token.split('.')
  if (parts.length < 2 || !parts[1]) return null

  const base64 = parts[1].replace(/-/g, '+').replace(/_/g, '/')
  try {
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    )
    return JSON.parse(jsonPayload)
  } catch {
    return null
  }
}

/** Build absolute URL under Nuxt app base */
function getAbsolute(path: string) {
  const base = useRuntimeConfig().app.baseURL || '/'
  const prefix = base === '/' ? '' : base
  return `${window.location.origin}${prefix}${path.startsWith('/') ? path : `/${path}`}`
}

export default defineNuxtPlugin({
  name: 'keycloak',
  parallel: true,
  setup: (nuxtApp) => {
    if (!process.client) return

    const config = useRuntimeConfig()
    const authStore = useAuthStore()
    const stateStore = useStateStore()
    const router = useRouter()

    // ป้องกัน init ซ้ำถ้ามี token อยู่แล้ว
    if (authStore.user?.token) {
      nuxtApp.provide('keycloak', null)
      authStore.ready = true
      return
    }

    // เริ่มต้นให้ UI แสดง AuthLoading จนกว่าจะเสร็จ
    authStore.ready = false

    const kc = new Keycloak({
      url: config.public.kcIssuerURI as string,
      realm: config.public.kcRealm as string,
      clientId: config.public.kcClientId as string
    })

    // สร้าง redirectUri ไป /auth/callback ภายใต้ baseURL
    const basePath = (config.public.baseURL as string) || '/'
    const redirectUri = `${window.location.origin}${basePath === '/' ? '' : basePath}/auth/callback`

    kc.init({
      onLoad: 'check-sso',
      silentCheckSsoRedirectUri: getAbsolute('silent-check-sso.html'),
      redirectUri,
      checkLoginIframe: false
    })
      .then(async (authenticated) => {
        if (authenticated && kc.token) {
          authStore.setAuthenticated(true)

          // decode token → userData เบื้องต้น
          const tokenPayload = parseJwt(kc.token)
          if (!tokenPayload?.sub) {
            // token แปลกๆ → logout
            await kc.logout()
            authStore.ready = true
            return
          }

          let userData: UserData = {
            id: tokenPayload.sub,
            username: tokenPayload.preferred_username ?? '',
            email: tokenPayload.email ?? '',
            firstName: tokenPayload.given_name ?? '',
            lastName: tokenPayload.family_name ?? '',
            districts: [],
            permissions: [],
            role: tokenPayload.role ?? '',
            token: kc.token,
            refreshToken: kc.refreshToken ?? '',
            avatar: tokenPayload.avatar ?? '',
            locale: tokenPayload.locale ?? 'th',
            fullName:
              `${tokenPayload.given_name ?? ''} ${tokenPayload.family_name ?? ''}`.trim() ||
              tokenPayload.preferred_username ||
              ''
          }

          authStore.setUser(userData)

          // Introspect (ถ้าจำเป็นในระบบคุณ)
          try {
            const introspect = await $fetch<IntrospectResponse>('/auth/introspect', {
              baseURL: config.public.apiBase,
              method: 'GET',
              headers: { Authorization: `Bearer ${kc.token}` }
            })

            if (introspect?.status) {
              const perms = (introspect.detail.permissions || []).map((p: any) =>
                typeof p === 'string' ? { resource: p, allow: 'r' } : p
              )
              userData = { ...userData, permissions: perms }
              authStore.setUser(userData)
            }
          } catch (err) {
            // ไม่เป็นไร ใช้สิทธิ์จาก token ไปก่อน
            // console.error('Introspect failed', err)
          }

          // เคลียร์ hash ของ keycloak (#state=..., #session_state=...)
          if (window.location.hash.includes('state=')) {
            window.history.replaceState({}, document.title, window.location.pathname + window.location.search)
          }

          // --- การนำทางหลัง auth ---
          // 1) returnTo จาก query (/auth/callback?returnTo=/foo?x=1)
          const url = new URL(window.location.href)
          const returnTo = url.searchParams.get('returnTo')

          // 2) intended จาก middleware เก็บไว้ก่อนถูกส่งไป login
          const intended = sessionStorage.getItem('intended')

          // 3) หน้าปัจจุบัน
          const currentPath = router.currentRoute.value.fullPath
          const currentPathOnly = router.currentRoute.value.path
          const publicPages = new Set<string>(['/', '/login', '/auth/callback'])

          const goDefaultByRole = async () => {
            if (userData.role === 'administrator') return navigateTo('/police', { replace: true })
            if (['management', 'user'].includes(userData.role || '')) return navigateTo('/watchman', { replace: true })
            if (userData.role === 'other') return navigateTo('/uturans', { replace: true })
            return navigateTo('/settings/devices', { replace: true })
          }

          // ลำดับความสำคัญ: returnTo > intended > (ถ้าตอนนี้อยู่ public page → ไป defaultByRole) > คงอยู่หน้าเดิม
          if (returnTo) {
            await navigateTo(returnTo, { replace: true })
          } else if (intended) {
            sessionStorage.removeItem('intended')
            await navigateTo(intended, { replace: true })
          } else if (publicPages.has(currentPathOnly)) {
            await goDefaultByRole()
          } else {
            // non-public page → refresh อยู่หน้าเดิม (ไม่ต้อง navigate)
          }

          // Auto refresh token
          const timer = window.setInterval(() => {
            kc.updateToken(30)
              .then((refreshed) => {
                if (refreshed) {
                  authStore.setUser({
                    ...authStore.user!,
                    token: kc.token || '',
                    refreshToken: kc.refreshToken || ''
                  })
                }
              })
              .catch(async () => {
                console.error('Token refresh failed → logout')
                await kc.logout()
              })
          }, 30_000)

          // เก็บ interval ไว้ปิดถ้าจำเป็น (optional)
          // @ts-ignore
          window.__kc_refresh_timer = timer
        } else {
          // not authenticated → ปล่อยให้ middleware ตัดสินใจ (จะส่งไป /login เมื่อจำเป็น)
        }
      })
      .catch((err) => {
        console.error('Keycloak init failed:', err)
      })
      .finally(() => {
        // ปลดล็อค UI ให้แสดงผล หน้าที่เหลือให้ middleware จัดการ
        authStore.ready = true
      })

    nuxtApp.provide('keycloak', kc)
  }
})
