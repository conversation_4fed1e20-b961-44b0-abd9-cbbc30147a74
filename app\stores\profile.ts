// app/stores/profile.ts
import { defineStore } from 'pinia'

// ✅ กำหนด Type Profile ตรงกับ Introspect API
export interface IntrospectProfile {
  sub: string
  username: string
  email: string
  role: string
  active: boolean
  given_name?: string
  family_name?: string
  avatar?: string
  locale?: string
  permissions: string[] // ✅ เก็บ permissions ใน profile เลย
  [key: string]: unknown // เผื่อฟิลด์อื่นที่อาจเพิ่มมาในอนาคต
}

// ✅ defineStore จาก Pinia (ไม่ต้องใช้ @pinia/nuxt)
export const useProfileStore = defineStore('profileStore', () => {
  // เก็บข้อมูลผู้ใช้ที่ introspect จาก Keycloak หรือ API
  const profile = ref<IntrospectProfile | null>(null)

  /** ✅ ตั้งค่า profile เต็มๆ */
  const setProfile = (data: IntrospectProfile) => {
    profile.value = data
  }

  /** ✅ อัปเดต permissions */
  const setPermissions = (perms: string[]) => {
    if (!profile.value) return
    profile.value.permissions = perms
  }

  /** ✅ เช็คว่าผู้ใช้มี permission นี้ไหม */
  const hasPermission = (perm: string) => {
    return profile.value?.permissions?.includes(perm) ?? false
  }

  return {
    profile,
    setProfile,
    setPermissions,
    hasPermission
  }
})
