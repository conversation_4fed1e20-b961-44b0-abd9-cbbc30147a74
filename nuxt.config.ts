// nuxt.config.ts
const baseEnv = process.env.NUXT_APP_BASE_URL || '/'
const basePath = baseEnv === '/' ? '/' : baseEnv.replace(/\/$/, '')
const joinPath = (prefix: string, path: string) =>
  (prefix === '/' ? '' : prefix) + path
const cleanBase = basePath.replace(/\/$/, '') || '' // '/' → ''
export default defineNuxtConfig({
  app: {
    baseURL: basePath,
    cdnURL: '',
    buildAssetsDir: '/_nuxt/',
  },
  modules: [
    '@nuxt/eslint',
    '@nuxt/content',
    '@nuxtjs/google-fonts',
    '@nuxtjs/i18n',
    '@nuxt/image',
    '@nuxt/ui-pro',
    '@vueuse/nuxt',
    '@pinia/nuxt'
  ],
  compatibilityDate: '2024-07-11',
  content: {
    preview: {
      dev: true,
      api: 'https://api.nuxt.studio',
      gitInfo: {
        name: 'klynx',                  // เช่น 'klynx' หรือ 'khwan-frontend'
        owner: 'pointitconsulting',         // เช่น 'pointitconsulting' หรือ 'klynx'
        url: 'https://github.com/pointitconsulting/klynx'  // ลิงก์ GitHub repo เดียวกับที่ Studio เลือก
      }
    }
  },
  css: ['~/assets/css/main.css'],
  devtools: {
    enabled: true
  },
  eslint: {
    config: {
      stylistic: {
        commaDangle: 'never',
        braceStyle: '1tbs'
      }
    }
  },
  googleFonts: {
    families: {
      // เพิ่มฟอนต์ Sarabun และสามารถเลือกน้ำหนัก (weight) ที่ต้องการได้
      Sarabun: [300, 400, 500, 700]
    },
    display: 'swap', // ป้องกันการกระพริบของฟอนต์
    download: true,  // ดาวน์โหลดฟอนต์มาไว้ที่ server เพื่อความเร็ว
  },
  i18n: {
    strategy: 'no_prefix',
    defaultLocale: 'th',
    locales: [
      {
        code: 'th',
        name: 'TH',
        file: 'th.ts'
      },
      {
        code: 'en',
        name: 'EN',
        file: 'en.ts'
      }
    ],
    langDir: 'lang/' // ✅ v8 ก็ยังรองรับ (แม้จะ deprecated ในอนาคต)
  },
  image: {
    // ตัวอย่างการใช้ IPX provider
    provider: 'ipx',
    format: ['webp', 'avif'], // auto convert
    domains: ['aliza-s3.k-lynx.com'], // ถ้าโหลดจาก S3
  },
  nitro: {
    externals: {
      inline: ['ofetch', 'ipx']
    },
    preset: 'node-server'
  },
  runtimeConfig: {
    public: {
      baseURL: basePath,
      apiUrl: process.env.NUXT_PUBLIC_API_BASE,
      apiBase: process.env.NUXT_PUBLIC_API_BASE ?? '',
      apiBaseWatchMan: process.env.NUXT_PUBLIC_API_BASE_WATCH_MAN ?? '',
      kcIssuerURI: process.env.KC_ISSUER_URI ?? '',
      kcRealm: process.env.KC_REALM ?? '', // ✅ เพิ่ม URL ของ Keycloak
      kcClientId: process.env.KC_CLIENT_FE_ID ?? '',

      googleMapsApiKey: process.env.GOOGLE_MAP_TOKEN,
      mqttServer: process.env.MQTT_BROKER || 'aliza.kudsonmoo.co',
      mqttURL: process.env.NUXT_PUBLIC_MQTT_URL || '',
      mqttUsername: process.env.NUXT_PUBLIC_MQTT_USERNAME || '',
      mqttPassword: process.env.NUXT_PUBLIC_MQTT_PASSWORD || '',
      mqttPort: process.env.MQTT_PORT || '30883',
      watchmanURL: process.env.WATCHMAN_URL,
      ibocURL: process.env.IBOC_URL,
      svmsURL: process.env.SVMS_URL,
      svmsPassword: process.env.SVMS_PASSWORD
    },
    private: {
      apiUrl: process.env.NUXT_PUBLIC_API_BASE
    }
  },
  routeRules: {
    '/api/**': { cors: true },
    [joinPath(basePath, '/kapi/**')]: { proxy: process.env.NUXT_PUBLIC_API_BASE },
    [joinPath(basePath, '/papi/**')]: { proxy: process.env.NUXT_PUBLIC_PAPI_BASE },
    '/docs': { redirect: '/docs/getting-started', prerender: false }
  },
  vite: {
    server: {
      allowedHosts: true,
      proxy: {
        [`^${cleanBase}/kapi`]: {
          target: process.env.NUXT_PUBLIC_API_BASE?.replace(/\/$/, '') + '/',
          changeOrigin: true,
          secure: false,
          rewrite: path => path.replace(new RegExp(`^${cleanBase}/kapi`), ''),
        },
        [`^${cleanBase}/sso`]: {
          target: process.env.NUXT_PUBLIC_API_BASE_REALM ?? '',
          changeOrigin: true,
          secure: false,
          rewrite: path => path.replace(new RegExp(`^${cleanBase}/sso`), ''),
        },
      },
      watch: {
        usePolling: true
      }
    }
  }
})
