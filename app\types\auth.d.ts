// app/types/auth.d.ts
export interface AuthState {
    isAuthenticated: boolean
    user: UserData | null
}

export interface Permission {
    resource: string
    allow: string
}

export interface UserData {
    id: string
    username: string
    email: string
    firstName: string
    lastName: string
    fullName: string
    districts: string[]
    permissions: Permission[]
    role: string
    token: string
    refreshToken: string
    avatar?: string
    locale: string
}

export interface Profile {
    sub: string
    username: string
    email: string
    role: string
    active: boolean
    permissions: string[]
    givenName: string
    familyName: string
    preferredUsername?: string
    avatar: string
    locale?: string
    exp?: number
    scope?: string
    [key: string]: unknown
}

export interface IntrospectResponse {
    detail: Profile
    status: boolean
}

export interface ResponseSignup {
    detail: Profile
    status: boolean
}

// types/keycloak.d.ts
export interface KCSession {
    token: string
    refreshToken?: string
    payload: any
    introspect?: any
}


// ~/types/systemUsers.ts
export interface UserRow {
    id: string
    username: string
    fullname: string
    phone: string
    email: string
    status: boolean
    role: string
    createdAt: string
    order: number
}

export interface GetUsersResponse {
    details: Array<{
        id: string
        username: string
        firstName?: string
        lastName?: string
        phone?: string
        email?: string
        enabled?: boolean
        role?: string
        createdAt?: string
    }>
    pagination?: {
        totalRecords: number
        page: number
        perPage: number
    }
}

export const useAuthStore = defineStore('auth', {
    state: () => ({
        isAuthenticated: false,
        user: null as UserData | null,
        token: '' as string,
        ready: false
    }),
    actions: {
        setUser(user: UserData) {
            this.user = user
            this.token = user.token
            this.isAuthenticated = true
            this.ready = true
        },
        logout() {
            this.user = null
            this.token = ''
            this.isAuthenticated = false
            this.ready = false
            localStorage.removeItem('auth')
        }
    }
})

export const resourceGroups = ref<Record<string, any[]>>({
    menu: [
        {
            name: 'Dashboard',
            ownerUserId: 'admin',
            assignedGroupId: 'group1',
            assignedRoleId: 'role1',
            crud: 'Read',
            action: ''
        }
        // Add more items as needed
    ]
    // Add more resource types as needed
})

export type PermissionCheck = {
    any?: string[]
    all?: string[]
    action?: string // c | r | u | d
}