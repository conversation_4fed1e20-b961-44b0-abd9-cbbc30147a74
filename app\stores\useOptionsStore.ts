// app/stores/profile.ts
import { defineStore } from 'pinia'

// ✅ กำหนด Type Profile ตรงกับ Introspect API
export interface useOptionsStoreTest {
  sub: string
  username: string
  email: string
  role: string
  active: boolean
  given_name?: string
  family_name?: string
  avatar?: string
  locale?: string
  permissions: string[] // ✅ เก็บ permissions ใน profile เลย
  [key: string]: unknown // เผื่อฟิลด์อื่นที่อาจเพิ่มมาในอนาคต
}

export const useOptionsStore = defineStore('optionsStore', () => {
  const profile = ref<useOptionsStoreTest | null>(null)

  const setProfile = (data: useOptionsStoreTest) => {
    profile.value = data
  }

  const setPermissions = (perms: string[]) => {
    if (!profile.value) return
    profile.value.permissions = perms
  }

  const hasPermission = (perm: string) => {
    return profile.value?.permissions?.includes(perm) ?? false
  }

  return {
    profile,
    setProfile,
    setPermissions,
    hasPermission
  }
})
