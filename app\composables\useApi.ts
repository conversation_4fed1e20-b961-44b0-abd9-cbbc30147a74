// app/composables/useApi.ts
import { useAuthStore } from '~/stores/auth'

type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'

// 1) Overload สำหรับ GET (ไม่มี body)
// 3) Implement จริง
export async function useApi<TResponse = any, TBody = any>(
  url: MaybeRefOrGetter<string>,
  options?: {
    method?: HttpMethod
    params?: Record<string, any>
    body?: TBody
    headers?: Record<string, string>
  }
): Promise<TResponse> {
  const config = useRuntimeConfig()
  const authStore = useAuthStore()
  const token = authStore.user?.token
  const rawUrl = toValue(url)
  const isDev = process.dev

  // ✅ Map URL prefix
  let finalUrl = rawUrl
  if (rawUrl.startsWith('/kapi')) {
    finalUrl = isDev
      ? rawUrl
      : `${config.public.apiBase}${rawUrl.replace(/^\/kapi/, '')}`
  } else if (rawUrl.startsWith('/sso')) {
    finalUrl = isDev
      ? rawUrl
      : `${config.public.kcIssuerURI}${rawUrl.replace(/^\/sso/, '')}`
  } else {
    finalUrl = `${config.public.baseURL.replace(/\/$/, '')}${rawUrl}`
  }

  const headers: Record<string, string> = {
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
    ...options?.headers,
  }

  // ✅ Cast response ให้เป็น TResponse
  const res = await $fetch<TResponse>(finalUrl, {
    method: options?.method || 'GET',
    params: options?.params,
    body: options?.body as any,
    headers,
  })

  return res as TResponse
}
export async function useApiWatchMan<TResponse = any, TBody = any>(
  url: MaybeRefOrGetter<string>,
  options?: {
    method?: HttpMethod
    params?: Record<string, any>
    body?: TBody
    headers?: Record<string, string>
  }
): Promise<TResponse> {
  const config = useRuntimeConfig()
  const authStore = useAuthStore()
  const token = authStore.user?.token
  const rawUrl = toValue(url)

  // ✅ Map URL prefix
  let finalUrl = rawUrl
  finalUrl = `${config.public.apiBaseWatchMan.replace(/\/$/, '')}${rawUrl}`
  
  const headers: Record<string, string> = {
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
    ...options?.headers,
  }

  // ✅ Cast response ให้เป็น TResponse
  const res = await $fetch<TResponse>(finalUrl, {
    method: options?.method || 'GET',
    params: options?.params,
    body: options?.body as any,
    headers,
  })

  return res as TResponse
}
