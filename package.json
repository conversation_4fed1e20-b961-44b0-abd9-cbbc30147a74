{"name": "klynx", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --no-fork --host", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "start": "node .output/server/index.mjs", "lint": "eslint .", "typecheck": "nuxt typecheck"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.10", "@googlemaps/markerclusterer": "^2.6.2", "@iconify-json/lucide": "^1.2.60", "@iconify-json/simple-icons": "^1.2.45", "@nuxt/content": "^3.6.3", "@nuxt/image": "^1.11.0", "@nuxt/ui-pro": "^3.3.0", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/i18n": "^10.0.3", "@pinia/nuxt": "^0.11.2", "@unovis/ts": "^1.5.2", "@unovis/vue": "^1.5.2", "@vueuse/nuxt": "^13.6.0", "better-sqlite3": "^12.2.0", "date-fns": "^4.1.0", "ipx": "^3.1.1", "keycloak-js": "^26.2.0", "mqtt": "^5.14.0", "nuxt": "^4.0.2", "ofetch": "^1.4.1", "zod": "^3.25.76"}, "devDependencies": {"@nuxt/eslint": "^1.7.1", "eslint": "^9.32.0", "typescript": "^5.9.2", "vue-tsc": "^3.0.5"}, "resolutions": {"unimport": "4.1.1"}, "pnpm": {"ignoredBuiltDependencies": ["@parcel/watcher", "esbuild", "maplibre-gl", "vue-demi"]}}