<!-- pages/map.vue -->
<script setup>
import { onMounted, ref, nextTick } from 'vue'
import { Loader } from '@googlemaps/js-api-loader'
import { useRuntimeConfig } from 'nuxt/app'
import { MarkerClusterer } from '@googlemaps/markerclusterer'
import { useColorMode } from '@vueuse/core'

// Constants
const locations = ref([])
const config = useRuntimeConfig()
const GOOGLE_KEY = config.public.googleMapsApiKey
const mapLoaded = ref(false)
const mapError = ref('')
let accessToken = ''
const map = ref(null)
const markers = []
const colorMode = useColorMode()
// กำหนด style ของ map
const lightStyle = []
const darkStyle = [
  { elementType: "geometry", stylers: [{ color: "#212121" }] },
  { elementType: "labels.text.stroke", stylers: [{ color: "#212121" }] },
  { elementType: "labels.text.fill", stylers: [{ color: "#757575" }] },
  {
    featureType: "road",
    elementType: "geometry",
    stylers: [{ color: "#383838" }]
  },
  {
    featureType: "water",
    elementType: "geometry",
    stylers: [{ color: "#000000" }]
  }
]
onMounted(async () => {
  const { $mqtt } = useNuxtApp()
  console.log($mqtt) // ตรวจว่ามี object หรือไม่
  console.log('colorMode',  colorMode.value);
  
  if ($mqtt) {
    $mqtt.subscribe('kcontrol-alarm', (err) => {
      if (err) {
        console.error('❌ Subscribe failed:', err)
      } else {
        console.log('✅ Subscribed to kcontrol-alarm')
      }
    })
  } else {
    console.error('❌ MQTT client not available')
  }

  console.log('GOOGLE_KEY', GOOGLE_KEY)

  // ตรวจสอบว่ามี API Key หรือไม่
  if (!GOOGLE_KEY) {
    mapError.value = 'Google Maps API Key is missing'
    console.error('Google Maps API Key is not configured')
    return
  }

  const userDataString = sessionStorage.getItem('userData')
  if (userDataString) {
    const userData = JSON.parse(userDataString)
    accessToken = userData.token
  }

  try {
    // โหลด Google Maps API
    const loader = new Loader({
      apiKey: GOOGLE_KEY,
      version: 'weekly',
      libraries: ['maps'] // เพิ่ม libraries
    })

    await loader.load()
    console.log('Google Maps API loaded successfully')

    // ดึงข้อมูลก่อน แล้วค่อยสร้างแผนที่
    await fetchKcontrolData()

    // รอให้ DOM พร้อม
    await nextTick()

    await initMap()
    mapLoaded.value = true
  } catch (error) {
    console.error('Error loading Google Maps:', error)
    mapError.value = `Error loading map: ${error.message}`
  }
})

async function fetchKcontrolData() {
  try {
    const result = await fetch(`${config.public.baseURL}api/proxy/kcontrol`, {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    })

    if (!result.ok) {
      throw new Error(`HTTP error! status: ${result.status}`)
    }

    const data = await result.json()
    locations.value = data.details
    console.log('Kcontrol data:', locations.value)
  } catch (error) {
    console.error('Error fetching kcontrol data:', error)
    // ใช้ข้อมูลทดสอบถ้าดึงข้อมูลไม่ได้
    locations.value = [
      { id: 1, name: 'Test Location', lat: '13.6178196', long: '100.6956348', district: 'Test District' }
    ]
  }
}

const markersMap = new Map()
const statusOverlaysMap = new Map()

function playAlertSound(status) {
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)()

    let frequency = 800 // default
    let duration = 500 // default

    switch (status) {
      case 'alarm':
        frequency = 1000 // เสียงแหลม
        duration = 1000 // เสียงนาน
        break
      case 'warning':
        frequency = 600 // เสียงกลาง
        duration = 300
        break
      case 'offline':
        frequency = 400 // เสียงต่ำ
        duration = 200
        break
      case 'online':
        frequency = 800 // เสียงปกติ
        duration = 150
        break
    }

    for (let i = 0; i < (status === 'alarm' ? 3 : 1); i++) {
      setTimeout(() => {
        const oscillator = audioContext.createOscillator()
        const gainNode = audioContext.createGain()

        oscillator.connect(gainNode)
        gainNode.connect(audioContext.destination)

        oscillator.frequency.value = frequency
        oscillator.type = 'sine'

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000)

        oscillator.start(audioContext.currentTime)
        oscillator.stop(audioContext.currentTime + duration / 1000)
      }, i * 200)
    }
  } catch (error) {
    console.error('Error playing alert sound:', error)
  }
}

function updateMarkerStatus(locationId, newStatus) {
  console.log(`Updating marker ${locationId} to status: ${newStatus}`)

  const marker = markersMap.get(locationId)
  if (marker) {
    marker.setIcon(createStatusMarker(newStatus))
    console.log(`✅ Marker ${locationId} icon updated`)
  } else {
    console.warn(`❌ Marker ${locationId} not found`)
  }

  const overlay = statusOverlaysMap.get(locationId)
  if (overlay) {
    overlay.onRemove()

    const location = locations.value.find(loc => loc.id === locationId)
    if (location) {
      const lat = parseFloat(location.lat)
      const lng = parseFloat(location.long)
      if (!isNaN(lat) && !isNaN(lng)) {
        const newOverlay = createStatusOverlay(newStatus, { lat, lng })
        newOverlay.setMap(map)
        statusOverlaysMap.set(locationId, newOverlay)
        console.log(`✅ Status overlay ${locationId} updated`)
      }
    }
  } else {
    console.warn(`❌ Status overlay ${locationId} not found`)
  }

  playAlertSound(newStatus)
}

function processMqttMessage(topic, message) {
  try {
    console.log(`Processing MQTT message from ${topic}:`, message)

    let messageData
    try {
      messageData = JSON.parse(message)
    } catch (e) {
      messageData = { message: message }
    }

    let locationId = messageData.locationId || messageData.id
    const newStatus = messageData.status || 'alarm'

    if (!locationId && messageData.hwId) {
      const location = locations.value.find(loc => loc.hwId === messageData.hwId)
      if (location) {
        locationId = location.id
      }
    }
    if (!locationId) {
      console.log('No specific location ID, updating all markers')
      locations.value.forEach((location) => {
        updateMarkerStatus(location.id, newStatus)
      })
      return
    }
    updateMarkerStatus(locationId, newStatus)
  } catch (error) {
    console.error('Error processing MQTT message:', error)
  }
}

function createStatusMarker(status) {
  console.log('status', status)
  const svgMarker = `<svg xmlns="http://www.w3.org/2000/svg" version="1.0" width="512.000000pt" height="512.000000pt" viewBox="0 0 512.000000 512.000000" preserveAspectRatio="xMidYMid meet">
<g transform="translate(0.000000,512.000000) scale(0.100000,-0.100000)" stroke="none">
<path d="M1288 5099 c-23 -12 -46 -35 -58 -59 -18 -36 -20 -59 -20 -279 l0 -239 -173 -4 c-172 -3 -173 -3 -239 -36 -77 -38 -130 -92 -167 -171 -24 -53 -26 -67 -29 -228 l-4 -173 -239 0 c-221 0 -243 -2 -279 -20 -45 -23 -80 -80 -80 -130 0 -50 35 -107 80 -130 36 -18 58 -20 280 -20 l240 0 0 -150 0 -150 -240 0 c-222 0 -244 -2 -280 -20 -45 -23 -80 -80 -80 -130 0 -50 35 -107 80 -130 36 -18 58 -20 280 -20 l240 0 0 -150 0 -150 -240 0 c-222 0 -244 -2 -280 -20 -45 -23 -80 -80 -80 -130 0 -50 35 -107 80 -130 36 -18 58 -20 280 -20 l240 0 0 -150 0 -150 -240 0 c-222 0 -244 -2 -280 -20 -45 -23 -80 -80 -80 -130 0 -50 35 -107 80 -130 36 -18 58 -20 280 -20 l240 0 0 -150 0 -150 -240 0 c-222 0 -244 -2 -280 -20 -45 -23 -80 -80 -80 -130 0 -50 35 -107 80 -130 36 -18 58 -20 279 -20 l239 0 4 -172 c3 -162 5 -176 29 -229 37 -79 90 -133 167 -171 66 -33 67 -33 239 -36 l173 -4 0 -239 c0 -221 2 -243 20 -279 23 -45 80 -80 130 -80 50 0 107 35 130 80 18 36 20 58 20 280 l0 240 150 0 150 0 0 -240 c0 -222 2 -244 20 -280 23 -45 80 -80 130 -80 50 0 107 35 130 80 18 36 20 58 20 280 l0 240 150 0 150 0 0 -240 c0 -222 2 -244 20 -280 23 -45 80 -80 130 -80 50 0 107 35 130 80 18 36 20 58 20 280 l0 240 150 0 150 0 0 -240 c0 -222 2 -244 20 -280 23 -45 80 -80 130 -80 50 0 107 35 130 80 18 36 20 58 20 280 l0 240 150 0 150 0 0 -240 c0 -222 2 -244 20 -280 23 -45 80 -80 130 -80 50 0 107 35 130 80 18 36 20 58 20 279 l0 239 173 4 c172 3 173 3 239 36 77 38 130 92 167 171 24 53 26 67 29 229 l4 172 239 0 c221 0 243 2 279 20 45 23 80 80 80 130 0 50 -35 107 -80 130 -36 18 -58 20 -280 20 l-240 0 0 150 0 150 240 0 c222 0 244 2 280 20 45 23 80 80 80 130 0 50 -35 107 -80 130 -36 18 -58 20 -280 20 l-240 0 0 150 0 150 240 0 c222 0 244 2 280 20 45 23 80 80 80 130 0 50 -35 107 -80 130 -36 18 -58 20 -280 20 l-240 0 0 150 0 150 240 0 c222 0 244 2 280 20 45 23 80 80 80 130 0 50 -35 107 -80 130 -36 18 -58 20 -280 20 l-240 0 0 150 0 150 240 0 c222 0 244 2 280 20 45 23 80 80 80 130 0 50 -35 107 -80 130 -36 18 -58 20 -279 20 l-239 0 -4 173 c-3 172 -3 173 -36 239 -38 77 -92 130 -171 167 -53 24 -67 26 -228 29 l-173 4 0 239 c0 221 -2 243 -20 279 -23 45 -80 80 -130 80 -50 0 -107 -35 -130 -80 -18 -36 -20 -58 -20 -280 l0 -240 -150 0 -150 0 0 240 c0 222 -2 244 -20 280 -23 45 -80 80 -130 80 -50 0 -107 -35 -130 -80 -18 -36 -20 -58 -20 -280 l0 -240 -150 0 -150 0 0 240 c0 222 -2 244 -20 280 -23 45 -80 80 -130 80 -50 0 -107 -35 -130 -80 -18 -36 -20 -58 -20 -280 l0 -240 -150 0 -150 0 0 240 c0 222 -2 244 -20 280 -23 45 -80 80 -130 80 -50 0 -107 -35 -130 -80 -18 -36 -20 -58 -20 -280 l0 -240 -150 0 -150 0 0 240 c0 222 -2 244 -20 280 -37 73 -127 99 -202 59z m2552 -1199 c26 -13 47 -34 60 -60 20 -39 20 -56 20 -1280 0 -1224 0 -1241 -20 -1280 -13 -26 -34 -47 -60 -60 -39 -20 -56 -20 -1280 -20 -1224 0 -1241 0 -1280 20 -26 13 -47 34 -60 60 -20 39 -20 56 -20 1280 0 1223 0 1241 20 1280 12 24 35 47 58 59 36 20 62 20 1280 21 1226 0 1243 0 1282 -20z"/>
<path d="M1500 2560 l0 -1060 1060 0 1060 0 0 1060 0 1060 -1060 0 -1060 0 0 -1060z"/>
</g>
</svg>`

  return {
    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(svgMarker),
    scaledSize: new google.maps.Size(40, 40),
    anchor: new google.maps.Point(20, 20)
  }
}

function createStatusOverlay(status, latlng) {
  let emoji = '🔴'
  let statusText = ''
  let bgColor = '#dc2626'

  switch (status) {
    case 'online':
      emoji = '🟢'
      statusText = ''
      bgColor = '#16a34a'
      break
    case 'warning':
      emoji = '🟡'
      statusText = ''
      bgColor = '#eab308'
      break
    case 'offline':
      emoji = '🔴'
      statusText = ''
      bgColor = '#dc2626'
      break
    case 'alarm':
      emoji = '🔥'
      statusText = ''
      bgColor = '#dc2626'
      break
  }

  class StatusOverlay extends google.maps.OverlayView {
    constructor(position, content, bgColor, status) {
      super()
      this.position = position
      this.content = content
      this.bgColor = bgColor
      this.status = status
      this.div = null
    }

    onAdd() {
      const div = document.createElement('div')
      let style = ''

      if (this.status === 'alarm') {
        style = `
          position: absolute;
          background: ${this.bgColor};
          color: white;
          width: 48px;
          height: 48px;
          border-radius: 50%;
          font-size: 28px;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 6px rgba(0,0,0,0.4);
          transform: translate(20%, -120%);
          pointer-events: none;
        `
      } else {
        style = `
          position: absolute;
          background: ${this.bgColor};
          color: white;
          border-radius: 12px;
          font-size: 12px;
          font-weight: bold;
          white-space: nowrap;
          box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          transform: translate(-200%, -150%);
          pointer-events: none;
        `
      }

      div.style.cssText = style
      div.innerHTML = this.content
      this.div = div

      const panes = this.getPanes()
      if (panes) {
        const targetPane = panes.floatPane || panes.overlayMouseTarget || panes.overlayLayer
        if (targetPane) {
          targetPane.appendChild(div)
        }
      }
    }

    draw() {
      if (!this.div) return
      const overlayProjection = this.getProjection()
      if (!overlayProjection) return

      const pixelPosition = overlayProjection.fromLatLngToDivPixel(this.position)
      if (pixelPosition) {
        this.div.style.left = pixelPosition.x + 'px'
        this.div.style.top = pixelPosition.y + 'px'
      }
    }

    onRemove() {
      if (this.div && this.div.parentNode) {
        this.div.parentNode.removeChild(this.div)
        this.div = null
      }
    }

    // bringToFront() {
    //   if (this.div) {
    //     this.div.style.zIndex = '999999'
    //   }
    // }
  }

  const position = new google.maps.LatLng(latlng.lat, latlng.lng)
  const content = `${emoji} ${statusText}`

  return new StatusOverlay(position, content, bgColor, status)
}
async function initMap() {
  try {
    const mapElement = document.getElementById('map')
    if (!mapElement) {
      throw new Error('Map element not found')
    }

    if (typeof google === 'undefined' || !google.maps) {
      throw new Error('Google Maps API not loaded')
    }
    const initialStyle = colorMode.value === 'dark' ? darkStyle : lightStyle
    map.value = new google.maps.Map(mapElement, {
      center: { lat: 13.6178196, lng: 100.6956348 },
      zoom: 12,
      mapTypeId: google.maps.MapTypeId.ROADMAP,
      styles: initialStyle
    })

    // map = new google.maps.Map(mapElement, {
    //   zoom: 12,
    //   center: { lat: 13.6178196, lng: 100.6956348 },
    //   mapTypeId: google.maps.MapTypeId.ROADMAP
    // })

    console.log('Map initialized:', map)
    watch(() => colorMode.value, (newTheme) => {
      if (!map.value) return
      map.value.setOptions({
        styles: newTheme === 'dark' ? darkStyle : lightStyle
      })
    })

    const infoWindow = new google.maps.InfoWindow()

    function getLocationStatus(location) {
      console.log('location 255', location)

      if (location && location.alarm === true) {
        return 'alarm'
      }

      if (location && typeof location.status === 'string') {
        if (location.status === 'online') return 'online'
      }

      if (location.stats) {
        const stats = location.stats
        if (stats.alarm > 0) return 'alarm'
        if (stats.warning > 0) return 'warning'
        if (stats.online > 0) return 'online'
        return 'offline'
      }

      if (typeof position !== 'undefined') {
        if (position.status === 'online') return 'online'
        if (position.stats) {
          if (position.stats.alarm > 0) return 'alarm'
          if (position.stats.warning > 0) return 'warning'
          if (position.stats.online > 0) return 'online'
        }
      }

      return 'offline'
    }

    if (locations.value && locations.value.length > 0) {
      locations.value.forEach((location, index) => {
        try {
          const lat = parseFloat(location.lat)
          const lng = parseFloat(location.long)

          if (isNaN(lat) || isNaN(lng)) {
            console.warn(`Invalid coordinates for location ${location.name}:`, location.lat, location.long)
            return
          }

          const locationStatus = getLocationStatus(location)

          const marker = new google.maps.Marker({
            position: { lat, lng },
            map: map,
            title: location.name || `Location ${index + 1}`,
            icon: createStatusMarker(locationStatus)
          })

          markersMap.set(location.id, marker)
          markers.push(marker)

          try {
            const statusOverlay = createStatusOverlay(locationStatus, { lat, lng })
            statusOverlay.setMap(map)

            statusOverlaysMap.set(location.id, statusOverlay)
          } catch (overlayError) {
            console.warn(`Could not create status overlay for ${location.name}:`, overlayError)
          }

          marker.addListener('click', () => {
            const statusInfo = {
              online: { text: 'พร้อมใช้งาน (Online)', color: '#16a34a', emoji: '🟢' },
              warning: { text: 'กำลังเตรียม, ระวัง', color: '#eab308', emoji: '🟡' },
              offline: { text: 'หยุด, มีปัญหา', color: '#dc2626', emoji: '🔴' },
              alarm: { text: 'ร้อนแรง, Alert', color: '#dc2626', emoji: '🔥' }
            }

            const currentStatus = statusInfo[locationStatus] || statusInfo['offline']

            const markerDetail = `
              <div>
                <h3 style="margin: 0 0 10px 0; color: #2563eb;">${location.name || 'Unknown Location'}</h3>

                <div
                  style="margin: 10px 0; padding: 8px; background: ${currentStatus.color}; color: white; border-radius: 6px; text-align: center; cursor: pointer; z-index: 999999 !important; position: relative;"
                  onclick="handleStatusClick('${location.id},${location.hwId}')"
                >
                  <strong>${currentStatus.emoji} ${currentStatus.text}</strong>
                </div>

                <p style="margin: 5px 0;"><strong>ID:</strong> ${location.id || 'N/A'}</p>
                <p style="margin: 5px 0;"><strong>District:</strong> ${location.district || 'N/A'}</p>
                <p style="margin: 5px 0;"><strong>Latitude:</strong> ${lat}</p>
                <p style="margin: 5px 0;"><strong>Longitude:</strong> ${lng}</p>
                <p style="margin: 5px 0;"><strong>Status:</strong> ${locationStatus}</p>
              </div>
            `

            infoWindow.setContent(markerDetail)
            infoWindow.open(map, marker)
          })

          console.log(`Marker created for ${location.name} at ${lat}, ${lng} with status: ${locationStatus}`)
        } catch (error) {
          console.error(`Error creating marker for location ${location.name}:`, error)
        }
      })

      if (markers.length > 0) {
        new MarkerClusterer({
          map: map,
          markers: markers
        })
      }

      const bounds = new google.maps.LatLngBounds()
      locations.value.forEach((location) => {
        const lat = parseFloat(location.lat)
        const lng = parseFloat(location.long)
        if (!isNaN(lat) && !isNaN(lng)) {
          bounds.extend(new google.maps.LatLng(lat, lng))
        }
      })

      if (locations.value.length > 1) {
        map.fitBounds(bounds)
      }
    } else {
      console.warn('No locations data available')
    }

    console.log('Map initialization completed')
    return { map, statusOverlays: statusOverlaysMap }
  } catch (error) {
    console.error('Error initializing map:', error)
    mapError.value = `Error initializing map: ${error.message}`
  }
}

window.handleStatusClick = async function (id, hwId) {
  try {
    const response = await fetch(`${config.public.baseURL}api/proxy/kcontrol/${id}/ack`, {
      method: 'PATCH',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({ hwId })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('API error:', errorText)
      throw new Error('Network response was not ok')
    }

    const data = await response.json()

    if (data.status === false) {
      alert(`${data.message} (ID: ${id})`)
    } else {
      alert('Acknowledged successfully!')
    }
  } catch (error) {
    console.error('handleStatusClick error:', error)
    alert('Failed to acknowledge.')
  }
}

</script>

<template>
  <UDashboardPanel class="full-screen-panel">
    <UDashboardPanel grow>
      <UDashboardNavbar title="Map" />
      <UContainer class="full-screen-container">
        <div v-if="mapError" class="error-message">
          <p>{{ mapError }}</p>
        </div>
        <div v-else-if="!mapLoaded" class="loading-message">
          <p>Loading map...</p>
        </div>
        <div id="map" class="full-size-map" :class="{ 'map-hidden': !mapLoaded }" />
      </UContainer>
      <UDashboardPanelContent />
    </UDashboardPanel>
  </UDashboardPanel>
</template>

<style scoped>
.full-screen-panel,
.full-screen-container {
  width: 100vw;
  height: 100vh;
  padding: 0;
  margin: 0;
}

.full-size-map {
  width: 100%;
  height: 100%;
}

.map-hidden {
  display: none;
}

/* optional: remove margins from UDashboardPanelContent */
.UDashboardPanelContent {
  margin: 0;
  padding: 0;
}

html,
body,
#__nuxt {
  height: 100%;
  margin: 0;
  padding: 0;
}

.map-container {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
}

.full-size-map {
  width: 100%;
  height: 100%;
  border: none;
  /* เอา border ออก ถ้าไม่อยากให้มี */
  border-radius: 0;
}

.map-hidden {
  display: none;
}

.error-message {
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.loading-message {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  color: #6b7280;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
}
</style>
