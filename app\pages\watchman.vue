<script setup lang="ts">
import { ref, watchEffect } from 'vue'

const route = useRoute()
const iframeUrl = ref<string | null>(null)

watchEffect(() => {
  iframeUrl.value = (route.query.url as string) || null
})
</script>

<template>
  <UDashboardPanel id="permissions" :ui="{ body: 'lg:py-0' }" class="h-screen">
    <template #header>
      <div class="iframe-container">
        <iframe
          v-if="iframeUrl"
          :src="iframeUrl"
          frameborder="0"
          class="iframe-full"
        ></iframe>
      </div>
    </template>
  </UDashboardPanel>
</template>

<style scoped>
.iframe-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 64px); /* ลบ header ถ้ามี */
}

.iframe-full {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}
</style>
