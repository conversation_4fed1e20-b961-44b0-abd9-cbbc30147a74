<script>
import DetectedFaceItem from './DetectedFaceItem.vue';
import AlertItem from './AlertItem.vue';

export default {
  name: 'RightSidebar',
  components: {
    DetectedFaceItem,
    AlertItem,
  },
  data() {
    return {
      detectedFaces: [
        { id: 1, image: '/images/logo-dark.webp', timestamp: '30/08/2567 10:35' },
        { id: 2, image: '/images/logo-dark.webp', timestamp: '30/08/2567 10:36' },
        { id: 3, image: '/images/logo-dark.webp', timestamp: '30/08/2567 10:36' },
        { id: 4, image: '/images/logo-dark.webp', timestamp: '30/08/2567 10:36' },
        { id: 5, image: '/images/logo-dark.webp', timestamp: '30/08/2567 10:36' },
        { id: 6, image: '/images/logo-dark.webp', timestamp: '30/08/2567 10:36' },
      ],
      alerts: [
        { id: 1, type: 'ตรวจพบ : เข้าพื้นที่เฝ้าระวัง', details: 'บุคคล', camera: 'Camera Group 1 (Camera 2)', time: '30/08/2567 15:45 น.' },
        { id: 2, type: 'ตรวจพบ : บุคคลต้องสงสัย', details: 'จักรยานยนต์', camera: 'Camera Group 1 (Camera 3)', time: '30/08/2567 15:46 น.' },
        { id: 3, type: 'ตรวจพบ : เข้าพื้นที่เฝ้าระวัง', details: 'บุคคล', camera: 'Camera Group 1 (Camera 3)', time: '30/08/2567 15:46 น.' },
        { id: 4, type: 'ตรวจพบ : เข้าพื้นที่เฝ้าระวัง', details: 'จักรยานยนต์', camera: 'Camera Group 1 (Camera 3)', time: '30/08/2567 15:46 น.' },
        { id: 5, type: 'ตรวจพบ : เข้าพื้นที่เฝ้าระวัง', details: 'บุคคล', camera: 'Camera Group 1 (Camera 3)', time: '30/08/2567 15:46 น.' },
        { id: 6, type: 'ตรวจพบ : เข้าพื้นที่เฝ้าระวัง', details: 'จักรยานยนต์', camera: 'Camera Group 1 (Camera 3)', time: '30/08/2567 15:46 น.' },
      ],
    };
  },
};
</script>

<template>
  <aside class="right-sidebar">
    <!-- Section: การตรวจจับ -->
    <div class="grid grid-cols-4 gap-1 items-center" style="background-color: #9B0000; color: white;">
      <div class="col-span-2 p-1 text-center text-[16px]">
        การตรวจจับ
      </div>
      <div class="col-span-2 flex items-center p-2">
        <input type="checkbox" id="hideInfo" v-model="hidePrivateInfo" class="mr-2" />
        <label for="hideInfo">ปิดบังข้อมูลส่วนตัว</label>
      </div>
    </div>
    <div class="grid grid-cols-5 gap-1 items-center">
      <div class="col-span-1 flex justify-center items-center">
        <svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M19.1241 0C16.8696 0 14.7074 0.842866 13.1133 2.34318C11.5191 3.84349 10.6235 5.87834 10.6235 8.0001C10.6235 10.1219 11.5191 12.1567 13.1133 13.657C14.7074 15.1573 16.8696 16.0002 19.1241 16.0002C21.3786 16.0002 23.5407 15.1573 25.1349 13.657C26.729 12.1567 27.6246 10.1219 27.6246 8.0001C27.6246 5.87834 26.729 3.84349 25.1349 2.34318C23.5407 0.842866 21.3786 0 19.1241 0ZM12.7487 8.0001C12.7487 6.40878 13.4204 4.88264 14.616 3.75741C15.8116 2.63218 17.4332 2.00003 19.1241 2.00003C20.8149 2.00003 22.4365 2.63218 23.6322 3.75741C24.8278 4.88264 25.4995 6.40878 25.4995 8.0001C25.4995 9.59142 24.8278 11.1176 23.6322 12.2428C22.4365 13.368 20.8149 14.0002 19.1241 14.0002C17.4332 14.0002 15.8116 13.368 14.616 12.2428C13.4204 11.1176 12.7487 9.59142 12.7487 8.0001ZM29.4628 29.5944C27.1868 30.9604 24.2478 31.7224 21.0431 31.9364C20.8831 31.5393 20.6354 31.1787 20.3163 30.8784L19.3812 29.9984C22.9621 29.9664 26.0966 29.2404 28.3195 27.9064C30.5509 26.5663 31.8749 24.6203 31.8749 22.0003C31.8749 21.4698 31.651 20.9611 31.2524 20.5861C30.8539 20.211 30.3134 20.0003 29.7497 20.0003H16.4549C16.1891 19.2944 15.8307 18.6225 15.3881 18.0002H29.7497C30.877 18.0002 31.958 18.4217 32.7551 19.1718C33.5522 19.922 34 20.9394 34 22.0003C34 25.3823 32.2298 27.9324 29.4628 29.5944ZM7.43584 30.0004C9.10619 30.0004 10.649 29.4804 11.8901 28.6064L17.3092 33.7064C17.4079 33.7994 17.525 33.8732 17.654 33.9236C17.7829 33.9739 17.9211 33.9999 18.0608 34C18.2004 34.0001 18.3386 33.9743 18.4677 33.9241C18.5967 33.8739 18.7139 33.8003 18.8127 33.7074C18.9115 33.6146 18.9899 33.5043 19.0434 33.383C19.097 33.2616 19.1246 33.1315 19.1247 33.0001C19.1248 32.8687 19.0974 32.7386 19.044 32.6172C18.9907 32.4958 18.9125 32.3854 18.8138 32.2924L13.3947 27.1924C14.3978 25.9299 14.9187 24.3857 14.8732 22.8099C14.8276 21.2341 14.2183 19.7189 13.1437 18.5096C12.0692 17.3004 10.5924 16.4678 8.95249 16.1469C7.31258 15.8259 5.60556 16.0352 4.10782 16.741C2.61008 17.4468 1.40931 18.6077 0.699951 20.0358C-0.00941338 21.4638 -0.185846 23.0754 0.199222 24.6096C0.584289 26.1437 1.50831 27.5107 2.82167 28.489C4.13503 29.4674 5.76084 29.9999 7.43584 30.0004ZM7.43584 28.0004C6.02679 28.0004 4.67545 27.4736 3.6791 26.5359C2.68275 25.5982 2.123 24.3264 2.123 23.0003C2.123 21.6742 2.68275 20.4024 3.6791 19.4647C4.67545 18.527 6.02679 18.0002 7.43584 18.0002C8.84489 18.0002 10.1962 18.527 11.1926 19.4647C12.1889 20.4024 12.7487 21.6742 12.7487 23.0003C12.7487 24.3264 12.1889 25.5982 11.1926 26.5359C10.1962 27.4736 8.84489 28.0004 7.43584 28.0004Z"
            fill="#9B0000" />
        </svg>
      </div>
      <div class="col-span-1 flex justify-center items-center">
        <svg width="34" height="34" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M4.16699 13.6451C4.34033 10.1451 4.85866 7.96175 6.41199 6.41175C7.96199 4.85841 10.1453 4.34008 13.6453 4.16675M35.8337 13.6451C35.6603 10.1451 35.142 7.96175 33.5887 6.41175C32.0387 4.85841 29.8553 4.34008 26.3553 4.16675M26.3553 35.8334C29.8553 35.6601 32.0387 35.1418 33.5887 33.5884C35.142 32.0384 35.6603 29.8551 35.8337 26.3551M13.6453 35.8334C10.1453 35.6601 7.96199 35.1418 6.41199 33.5884C4.85866 32.0384 4.34033 29.8551 4.16699 26.3551M29.167 28.3334L28.8303 26.9184C28.6934 26.3442 28.4065 25.8167 27.9989 25.3898C27.5912 24.9628 27.0776 24.6518 26.5103 24.4884L22.5003 23.3317V20.8867C23.9937 19.8784 25.0003 17.9934 25.0003 15.8334C25.0003 12.6117 22.7603 10.0001 20.0003 10.0001C17.2387 10.0001 15.0003 12.6117 15.0003 15.8334C15.0003 17.9934 16.0053 19.8784 17.5003 20.8867V23.3317L13.5153 24.4984C12.9651 24.6595 12.4657 24.9598 12.0654 25.3703C11.6651 25.7808 11.3775 26.2876 11.2303 26.8417L10.8337 28.3334"
            stroke="#9B0000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </div>
      <div class="col-span-1 flex justify-center items-center">
        <svg width="34" height="34" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M33.214 21.0001L29.7514 11.5594C29.5743 11.0778 29.2537 10.662 28.8329 10.3683C28.4121 10.0746 27.9114 9.91696 27.3982 9.91675H12.8149C11.7649 9.91675 10.8245 10.5724 10.4629 11.5594L6.99902 21.0001"
            stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M15.167 15.75C16.1335 15.75 16.917 14.9665 16.917 14C16.917 13.0335 16.1335 12.25 15.167 12.25C14.2005 12.25 13.417 13.0335 13.417 14C13.417 14.9665 14.2005 15.75 15.167 15.75Z"
            stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M11.2021 20.9999L11.6886 18.3563C11.7476 18.0339 11.9178 17.7424 12.1696 17.5327C12.4214 17.3229 12.7388 17.2081 13.0665 17.2083H17.2665C17.594 17.208 17.9113 17.3227 18.163 17.5322C18.4148 17.7418 18.5851 18.0329 18.6443 18.3551L19.1308 20.9999"
            stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M13.4167 21C13.4167 20.5359 13.601 20.0908 13.9292 19.7626C14.2574 19.4344 14.7025 19.25 15.1667 19.25C15.6308 19.25 16.0759 19.4344 16.4041 19.7626C16.7323 20.0908 16.9167 20.5359 16.9167 21M8.16667 31.15V33.8333C8.16667 33.988 8.22812 34.1364 8.33752 34.2458C8.44692 34.3552 8.59529 34.4167 8.75 34.4167H12.8333C12.9099 34.4167 12.9858 34.4016 13.0566 34.3723C13.1273 34.3429 13.1916 34.3 13.2458 34.2458C13.3 34.1916 13.3429 34.1273 13.3723 34.0566C13.4016 33.9858 13.4167 33.9099 13.4167 33.8333V31.15M19.25 31.15H6.41667C5.77033 31.15 5.25 30.6297 5.25 29.9833V22.1667C5.25 21.5203 5.77033 21 6.41667 21H33.8333C34.4797 21 35 21.5203 35 22.1667V26.4833"
            stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M11.083 27.825C12.0495 27.825 12.833 27.0414 12.833 26.075C12.833 25.1085 12.0495 24.325 11.083 24.325C10.1165 24.325 9.33301 25.1085 9.33301 26.075C9.33301 27.0414 10.1165 27.825 11.083 27.825Z"
            stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M27.4648 26.4833C27.4329 26.3495 27.4168 26.2125 27.417 26.075C27.417 25.6108 27.6014 25.1657 27.9296 24.8375C28.2577 24.5093 28.7029 24.325 29.167 24.325C29.6311 24.325 30.0762 24.5093 30.4044 24.8375C30.7326 25.1657 30.917 25.6108 30.917 26.075C30.9168 26.2125 30.9003 26.3496 30.868 26.4833"
            stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M49.5833 26.4834H20.4167C19.7723 26.4834 19.25 27.0057 19.25 27.6501V44.9167C19.25 45.5611 19.7723 46.0834 20.4167 46.0834H49.5833C50.2277 46.0834 50.75 45.5611 50.75 44.9167V27.6501C50.75 27.0057 50.2277 26.4834 49.5833 26.4834Z"
            stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M22.458 30.1584C22.9413 30.1584 23.333 29.7667 23.333 29.2834C23.333 28.8002 22.9413 28.4084 22.458 28.4084C21.9748 28.4084 21.583 28.8002 21.583 29.2834C21.583 29.7667 21.9748 30.1584 22.458 30.1584Z"
            fill="#9B0000" />
          <path
            d="M24.792 30.1584C25.2752 30.1584 25.667 29.7667 25.667 29.2834C25.667 28.8002 25.2752 28.4084 24.792 28.4084C24.3087 28.4084 23.917 28.8002 23.917 29.2834C23.917 29.7667 24.3087 30.1584 24.792 30.1584Z"
            fill="#9B0000" />
          <path
            d="M28.875 30.1584C29.3582 30.1584 29.75 29.7667 29.75 29.2834C29.75 28.8002 29.3582 28.4084 28.875 28.4084C28.3918 28.4084 28 28.8002 28 29.2834C28 29.7667 28.3918 30.1584 28.875 30.1584Z"
            fill="#9B0000" />
          <path
            d="M31.208 30.1584C31.6913 30.1584 32.083 29.7667 32.083 29.2834C32.083 28.8002 31.6913 28.4084 31.208 28.4084C30.7248 28.4084 30.333 28.8002 30.333 29.2834C30.333 29.7667 30.7248 30.1584 31.208 30.1584Z"
            fill="#9B0000" />
          <path
            d="M26.8337 37.45C27.9612 37.45 28.8753 36.5359 28.8753 35.4084C28.8753 34.2808 27.9612 33.3667 26.8337 33.3667C25.7061 33.3667 24.792 34.2808 24.792 35.4084C24.792 36.5359 25.7061 37.45 26.8337 37.45Z"
            stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M22.167 43.5751C22.167 42.3374 22.6587 41.1505 23.5338 40.2753C24.409 39.4001 25.596 38.9084 26.8337 38.9084C28.0713 38.9084 29.2583 39.4001 30.1335 40.2753C31.0087 41.1505 31.5003 42.3374 31.5003 43.5751"
            stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M47.8337 36.5751H34.417V43.5751H47.8337V36.5751ZM47.8337 36.5751V34.2418H37.917V36.5751H47.8337ZM40.2503 34.2418V36.5751M36.7503 38.9084H45.5003M36.7503 41.2418H45.5003M22.167 31.9084H31.5003V43.5751H22.167V31.9084ZM45.5003 29.2834H47.8337V31.6168H45.5003V29.2834Z"
            stroke="#9B0000" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </div>
      <div class="col-span-1 flex justify-center items-center">
        <svg width="34" height="34" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_118_12443)">
            <path
              d="M18.3129 16.9695C18.9952 16.6322 19.7084 16.3614 20.4426 16.1609L15.887 3.89506C15.7252 3.45914 15.4339 3.08319 15.0522 2.8177C14.6704 2.55221 14.2166 2.40991 13.7516 2.40991C13.2867 2.40991 12.8328 2.55221 12.4511 2.8177C12.0694 3.08319 11.778 3.45914 11.6162 3.89506L2.27733 28.7684C2.13726 29.1195 2.0868 29.5 2.1305 29.8755C2.1742 30.2511 2.31067 30.6098 2.52762 30.9194C2.74457 31.229 3.03518 31.4798 3.37322 31.649C3.71127 31.8183 4.08612 31.9007 4.46399 31.8889H12.3679C12.0923 31.1522 11.8939 30.3888 11.7757 29.6112H4.46399L13.7345 4.69228L18.3129 16.9695Z"
              fill="#9B0000" />
            <path
              d="M36.444 10.25H25.0551C24.451 10.25 23.8717 10.49 23.4445 10.9171C23.0173 11.3443 22.7773 11.9237 22.7773 12.5278V15.7736H23.5632C24.0612 15.7629 24.5594 15.7819 25.0551 15.8306V12.5278H36.444V23.9167H34.9065C35.1519 24.6572 35.3236 25.4202 35.419 26.1944H36.444C36.7431 26.1944 37.0393 26.1355 37.3157 26.0211C37.592 25.9066 37.8431 25.7388 38.0546 25.5273C38.2662 25.3158 38.4339 25.0647 38.5484 24.7883C38.6629 24.512 38.7218 24.2158 38.7218 23.9167V12.5278C38.7218 11.9237 38.4818 11.3443 38.0546 10.9171C37.6275 10.49 37.0481 10.25 36.444 10.25Z"
              fill="#9B0000" />
            <path
              d="M23.5634 18.0059C21.6487 18.0059 19.7771 18.5736 18.1851 19.6373C16.5932 20.701 15.3524 22.2129 14.6197 23.9818C13.887 25.7507 13.6953 27.6972 14.0688 29.575C14.4424 31.4528 15.3643 33.1778 16.7182 34.5316C18.072 35.8855 19.7969 36.8074 21.6748 37.181C23.5526 37.5545 25.4991 37.3628 27.268 36.6301C29.0369 35.8974 30.5487 34.6566 31.6125 33.0646C32.6762 31.4727 33.2439 29.601 33.2439 27.6864C33.2409 25.1199 32.22 22.6594 30.4052 20.8446C28.5904 19.0298 26.1299 18.0089 23.5634 18.0059ZM23.5634 35.0892C22.0992 35.0892 20.668 34.655 19.4506 33.8416C18.2332 33.0282 17.2844 31.872 16.7241 30.5193C16.1638 29.1667 16.0172 27.6782 16.3028 26.2422C16.5885 24.8062 17.2935 23.4872 18.3288 22.4519C19.3641 21.4166 20.6832 20.7115 22.1192 20.4259C23.5552 20.1402 25.0436 20.2868 26.3963 20.8471C27.749 21.4074 28.9051 22.3563 29.7186 23.5737C30.532 24.791 30.9662 26.2223 30.9662 27.6864C30.9631 29.6488 30.1822 31.53 28.7946 32.9176C27.407 34.3053 25.5258 35.0862 23.5634 35.0892Z"
              fill="#9B0000" />
          </g>
          <defs>
            <clipPath id="clip0_118_12443">
              <rect width="41" height="41" fill="white" />
            </clipPath>
          </defs>
        </svg>
      </div>
      <div class="col-span-1 flex justify-center items-center">
        <svg width="34" height="34" viewBox="0 0 38 34" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M32.2072 3.92461C31.5409 6.61406 29.1395 8.5 26.3885 8.5C23.6374 8.5 21.236 6.61406 20.5697 3.92461L19.9891 1.60703C19.7121 0.471484 18.5641 -0.219141 17.4294 0.0664062C16.2947 0.351953 15.6152 1.50078 15.8989 2.64297L16.4728 4.96055C17.1985 7.88242 19.1249 10.2465 21.6385 11.5746V31.875C21.6385 33.0504 22.5819 34 23.7496 34C24.9173 34 25.8607 33.0504 25.8607 31.875V23.375H26.9162V31.875C26.9162 33.0504 27.8596 34 29.0273 34C30.1951 34 31.1385 33.0504 31.1385 31.875V11.5746C33.652 10.2465 35.5784 7.88242 36.3041 4.96055L36.878 2.64297C37.1617 1.50742 36.4756 0.351953 35.3409 0.0664062C34.2062 -0.219141 33.0648 0.471484 32.7878 1.60703L32.2138 3.92461H32.2072ZM26.3885 6.375C27.2283 6.375 28.0338 6.03918 28.6276 5.4414C29.2215 4.84363 29.5551 4.03288 29.5551 3.1875C29.5551 2.34212 29.2215 1.53137 28.6276 0.933597C28.0338 0.335825 27.2283 0 26.3885 0C25.5486 0 24.7431 0.335825 24.1493 0.933597C23.5554 1.53137 23.2218 2.34212 23.2218 3.1875C23.2218 4.03288 23.5554 4.84363 24.1493 5.4414C24.7431 6.03918 25.5486 6.375 26.3885 6.375ZM5.27734 6.375C6.1172 6.375 6.92265 6.03918 7.51652 5.4414C8.11038 4.84363 8.44401 4.03288 8.44401 3.1875C8.44401 2.34212 8.11038 1.53137 7.51652 0.933597C6.92265 0.335825 6.1172 0 5.27734 0C4.43749 0 3.63204 0.335825 3.03817 0.933597C2.44431 1.53137 2.11068 2.34212 2.11068 3.1875C2.11068 4.03288 2.44431 4.84363 3.03817 5.4414C3.63204 6.03918 4.43749 6.375 5.27734 6.375ZM4.74957 8.5C2.42075 8.5 0.527344 10.4059 0.527344 12.75V31.875C0.527344 33.0504 1.47075 34 2.63845 34C3.80616 34 4.74957 33.0504 4.74957 31.875V23.375H5.80512V31.875C5.80512 33.0504 6.74852 34 7.91623 34C9.08394 34 10.0273 33.0504 10.0273 31.875V16.7809L10.885 18.1422C11.2742 18.7531 11.9471 19.1316 12.6662 19.1316H15.8329C17.0006 19.1316 17.944 18.182 17.944 17.0066C17.944 15.8313 17.0006 14.8816 15.8329 14.8816H13.8273L11.36 10.9703C10.3968 9.42969 8.7145 8.5 6.90686 8.5H4.74957Z"
            fill="#9B0000" />
        </svg>
      </div>
    </div>
    <!-- Section: บุคคลที่อยู่ล่วงหน้า -->
    <div class="detected-faces">
      <!-- <h3>บุคคลที่อยู่ล่วงหน้า</h3> -->
      <div class="face-grid">
        <DetectedFaceItem v-for="face in detectedFaces" :key="face.id" :face="face" :hide-info="hidePrivateInfo" />
      </div>
    </div>

    <!-- Section: การแจ้งเตือนจุดเสี่ยง -->
    <div class="grid grid-cols-1 gap-1 items-center" style="background-color: #9B0000; color: white;">
      <h3>การแจ้งเตือนจุดเสี่ยง</h3>
    </div>
    <div class="alerts-list">

      <AlertItem v-for="alert in alerts" :key="alert.id" :alert="alert" />
    </div>
  </aside>
</template>


<style scoped>
.right-sidebar {
  background-color: #f0f2f5;
  padding: 5px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  /* Scroll if content overflows */
}

.section-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-card h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.detection-options {
  display: flex;
  justify-content: space-around;
  /* margin-bottom: 15px; */
}

.option-button {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background-color: #f9f9f9;
  cursor: pointer;
  font-size: 14px;
  color: #555;
  transition: background-color 0.3s, border-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.option-button.active {
  background-color: #007bff;
  color: #fff;
  border-color: #007bff;
}

.face-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
</style>