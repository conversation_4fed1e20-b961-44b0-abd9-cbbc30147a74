// app/middleware/auth.global.ts
import { defineNuxtRouteMiddleware, navigateTo, abortNavigation } from '#app'
import { useAuthStore } from '~/stores/auth'

const PUBLIC_ROUTES = new Set<string>([
    '/', '/login', '/auth/callback', '/silent-check-sso.html'
])
const PUBLIC_PREFIXES = ['/docs', '/changelog'] // เพิ่มได้ตามต้องการ

export default defineNuxtRouteMiddleware((to, from) => {
    const auth = useAuthStore()

    // รอให้ init เสร็จ (Keycloak plugin จะตั้ง ready=true เอง)
    if (!auth.ready) return

    // public ถ้ามี meta.public=true หรือเป็น route/prefix ที่อนุญาต
    const isPublic =
        to.meta.public === true ||
        PUBLIC_ROUTES.has(to.path) ||
        PUBLIC_PREFIXES.some(p => to.path === p || to.path.startsWith(p + '/'))

    if (isPublic) return

    // ยังไม่ล็อกอิน → เก็บ intended แล้วส่งไปหน้า /
    if (!auth.isAuthenticated) {
        if (process.client) sessionStorage.setItem('intended', to.fullPath)
        if (to.path === '/') return abortNavigation() // กันวน
        return navigateTo('/', { replace: true })
    }
})
