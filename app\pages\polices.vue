<script setup lang="ts">
import { useApiWatchMan } from '~/composables/useApi'
import type { GetUsersResponse, UserWatch } from '~/types/'

const { t } = useI18n()
const router = useRouter()
// Columns สำหรับ UTable (ใช้ computed เพื่อ reactive กับ i18n)
const columns = computed(() => [
  { accessorKey: 'order', header: t('watchman.table.order') },

  {
    accessorKey: 'imagePath',
    header: t('watchman.table.image'),
    cell: (row: any) => {
      const imgUrl = row?.row?.original?.image || '/images/logo-dark.webp'
      return h('img', {
        src: imgUrl,
        alt: 'avatar',
        width: 50,
        height: 50,
        style: 'border-radius: 4px; object-fit: cover;',
        onError: (e: Event) => {
          (e.target as HTMLImageElement).src = '/images/logo-dark.webp'
        }
      })
    }
  },

  { accessorKey: 'prefix', header: t('watchman.table.prefix') },
  { accessorKey: 'fname', header: t('watchman.table.fname') },
  { accessorKey: 'lname', header: t('watchman.table.lname') },
  { accessorKey: 'nickname', header: t('watchman.table.nickname') },
  { accessorKey: 'age', header: t('watchman.table.age') },
  { accessorKey: 'policeStation', header: t('watchman.table.policeStation') },
  {
    accessorKey: 'actions',
    header: t('watchman.table.action'),
    cell: (row: any) => {
      return h('div', { class: 'flex gap-2' }, [
        h('button', {
          onClick: (e: Event) => {
            e.stopPropagation()
            router.push(`/watchman/edit/${row.row.original.id}`)
          },
          class: 'inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 transition-colors'
        }, [
          h('svg', {
            class: 'w-3 h-3 mr-1',
            fill: 'none',
            stroke: 'currentColor',
            viewBox: '0 0 24 24'
          }, [
            h('path', {
              strokeLinecap: 'round',
              strokeLinejoin: 'round',
              strokeWidth: '2',
              d: 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z'
            })
          ]),
          'แก้ไข'
        ])
      ])
    }
  }
])


// State
const q = ref('')
const page = ref(1)
const pageSize = ref(10)
const pending = ref(false)
const loadingDetail = ref(false)
const userResponse = ref<{ users: UserWatch[]; total: number }>({ users: [], total: 0 })

const users = computed(() => userResponse.value.users)
const total = computed(() => userResponse.value.total)
const selectedUser = ref<any | null>(null)
const showDetail = ref(false)

// Map API → UserWatch
function mapUserToRow(u: any, index: number): UserWatch {
  const fullParts = (u.fullname || '').trim().split(' ')
  const prefix = fullParts[0] || ''
  const fname = fullParts[1] || ''
  const lname = fullParts.slice(2).join(' ') || ''

  return {
    id: u.id,
    image: u.imagePath,
    prefix,
    fname,
    lname,
    nickname: u.nickname || '-',
    age: u.age || '-',
    policeStation: u.personType || '-',
    order: (page.value - 1) * pageSize.value + (index + 1),
  }
}

// Fetch Users
const fetchUsers = async () => {
  try {
    pending.value = true
    const resp = await useApiWatchMan<{ details: any[]; pagination?: { totalRecords: number } }>(
      '/WatchmanData/api_list_person.php',
      {
        params: { page: page.value, perPage: pageSize.value, q: q.value },
      }
    )

    userResponse.value = {
      users: (resp.details || []).map((u, idx) => mapUserToRow(u, idx)),
      total: resp.pagination?.totalRecords || resp.details?.length || 0,
    }
  } catch (err) {
    console.error('Error fetching users:', err)
  } finally {
    pending.value = false
  }
}

const fetchUserDetail = async (id: number) => {
  try {
    loadingDetail.value = true
    const resp = await useApiWatchMan<{ details: any, status: boolean }>(
      `/WatchmanData/api_get_person.php`,
      {
        params: {
          findby: 'id',
          id,
        },
      }
    )

    if (resp.status && resp.details) {
      selectedUser.value = resp.details
      showDetail.value = true
    }
  } catch (err) {
    console.error('Error fetching user detail:', err)
  } finally {
    loadingDetail.value = false
  }
}

// Handle row click
const handleRowClick = async (row: any) => {
  const userId = row.original.id
  if (userId) {
    await fetchUserDetail(userId)
  }
}

// Close detail panel
const closeDetail = () => {
  showDetail.value = false
  selectedUser.value = null
}

onMounted(fetchUsers)
watch([page, q], fetchUsers)
</script>

<template>
  <UDashboardPanel id="users">
    <template #header>
      <UDashboardNavbar :title="t('systemUsers.title')">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <WatchmanAddModal />
        </template>
      </UDashboardNavbar>
    </template>


    <template #body>
      <div class="flex flex-col gap-2 !gap-2">


        <div class="flex flex-wrap items-center justify-between gap-1.5 mb-1">
          <UInput v-model="q" class="max-w-sm" icon="i-lucide-search"
            :placeholder="t('systemUsers.searchPlaceholder')" />
        </div>
        <!-- Detail Panel -->
        <div v-if="showDetail" class="mt-1 p-2 bg-gray-50 border border-gray-200 rounded-lg shadow-sm">
          <div class="flex items-center justify-between mb-1">
            <h3 class="text-lg font-semibold text-gray-800">รายละเอียดข้อมูล</h3>
            <UButton icon="i-lucide-x" variant="ghost" size="sm" @click="closeDetail" class="hover:bg-gray-200" />
          </div>

          <div v-if="loadingDetail" class="flex items-center justify-center py-8">
            <USpinner size="lg" />
            <span class="ml-2 text-gray-600">กำลังโหลดข้อมูล...</span>
          </div>

          <div v-else-if="selectedUser"
            class="grid grid-cols-1 md:grid-cols-10 gap-4 text-sm sticky top-0 bg-white z-10 p-2 border-b border-gray-200">

            <div class="md:col-span-1">
              <h5 class="font-semibold mb-2">ภาพถ่าย</h5>
              <div class="flex items-center space-x-4 flex-col">
                <img :src="selectedUser.imagePath || '/images/logo-dark.webp'" alt="ภาพถ่าย"
                  class="w-40 h-auto rounded shadow object-cover" />
              </div>
            </div>
            <div class="md:col-span-3 space-y-3">
              <h5 class="font-semibold mb-2">ข้อมูลส่วนตัว</h5>
              <div class="flex">
                <span class="font-medium text-gray-700 mr-2.5">เลขประจำตัวประชาชน:</span>
                <span class="text-gray-900">{{ selectedUser.idcard || '-' }}</span>
              </div>
              <div class="flex">
                <span class="font-medium text-gray-700 mr-2.5">คำนำหน้า:</span>
                <span class="text-gray-900">{{ selectedUser.titlename || '-' }}</span>
              </div>
              <div class="flex">
                <span class="font-medium text-gray-700 mr-2.5">ชื่อ-นามสกุล:</span>
                <span class="text-gray-900">{{ selectedUser.firstname }} {{
                  selectedUser.lastname }}</span>
              </div>
              <div class="flex">
                <span class="font-medium text-gray-700 mr-2.5">ชื่อเล่น:</span>
                <span class="text-gray-900">{{ selectedUser.nickname || '-' }}</span>
              </div>
            </div>
            <div class="md:col-span-3 space-y-3">
              <h5>&nbsp;</h5>
              <div class="flex">
                <span class="font-medium text-gray-700 mr-2.5">วันเกิด:</span>
                <span class="text-gray-900">{{ selectedUser.birthday || '-' }}</span>
              </div>
              <div class="flex">
                <span class="font-medium text-gray-700 mr-2.5">อายุ:</span>
                <span class="text-gray-900">{{ selectedUser.age || '-' }}</span>
              </div>
              <div class="flex">
                <span class="font-medium text-gray-700 mr-2.5">เพศ:</span>
                <span class="text-gray-900">{{ selectedUser.sex || '-' }}</span>
              </div>
              <div class="flex">
                <span class="font-medium text-gray-700 mr-2.5">สถานภาพสมรส:</span>
                <span class="text-gray-900">{{ selectedUser.maritalStatus || '-' }}</span>
              </div>
            </div>

            <div class="md:col-span-3 space-y-3">
              <h5 class="font-semibold mb-2">ข้อมูลส่วนตัว</h5>
              <div class="flex">
                <span class="font-medium text-gray-700 mr-2.5">สถานะชีวิต:</span>
                <span class="text-gray-900">{{ selectedUser.deathDetail || '-' }}</span>
              </div>
              <div class="flex">
                <span class="font-medium text-gray-700 mr-2.5">สถานีตำรวจ:</span>
                <span class="text-gray-900">{{ selectedUser.policeStationDetail || '-' }}</span>
              </div>
              <div class="flex">
                <span class="font-medium text-gray-700 mr-2.5">ผู้บันทึก:</span>
                <span class="text-gray-900">{{ selectedUser.userRecorder || '-' }}</span>
              </div>
              <div class="flex">
                <span class="font-medium text-gray-700 mr-2.5">ตำแหน่ง:</span>
                <span class="text-gray-900">{{ selectedUser.userPosition || '-' }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="flex flex-col flex-1 overflow-y-auto" style="max-height: 500px;">
          <UTable :columns="columns" :data="users" :loading="pending" class="rounded-lg cursor-pointer"
            @select="handleRowClick">
            <template #loading-state>
              <div class="flex items-center justify-center py-8">
                <USpinner size="lg" />
                <span class="ml-2">กำลังโหลดข้อมูล...</span>
              </div>
            </template>

            <template #empty-state>
              <div class="flex flex-col items-center justify-center py-8 text-gray-500">
                <UIcon name="i-lucide-users" class="w-8 h-8 mb-2" />
                <p>ไม่พบข้อมูลผู้ใช้</p>
              </div>
            </template>
          </UTable>

          <div class="flex items-center justify-between gap-3 border-t border-default pt-4 mt-2">
            <div class="text-sm text-muted">
              {{ t('systemUsers.pagination.showing') }}
              {{ (page - 1) * pageSize + 1 }} -
              {{ Math.min(page * pageSize, total) }}
              {{ t('systemUsers.pagination.of') }} {{ total }}
              {{ t('systemUsers.pagination.users') }}
            </div>
            <UPagination v-model:page="page" :items-per-page="pageSize" :total="total" />
          </div>
        </div>

      </div>
    </template>
  </UDashboardPanel>
</template>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}

.cursor-pointer tbody tr:hover {
  background-color: #f9fafb;
}
</style>