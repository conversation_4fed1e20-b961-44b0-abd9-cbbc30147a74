<!-- app/pages/auth/callback.vue -->
<script setup lang="ts">
import AuthLoading from '~/components/AuthLoading.vue'
import { useProcessAuthCallback } from '~/composables/useProcessAuthCallback'

onMounted(async () => {
  const run = useProcessAuthCallback()
  await run()
})
</script>

<template>
  <AuthLoading />
</template>


<!-- <script setup lang="ts">
import { useAuthStore } from "~/stores/auth"
import { useRoute } from "#imports"
const { t } = useI18n()
const route = useRoute()
const authStore = useAuthStore();

onMounted(async () => {
  const token = route.query.token as string || ""
  const refreshToken = route.query.refresh_token as string || ""
  const userData = route.query.user ? JSON.parse(route.query.user as string) : null

  if (token) {
    // ✅ 1) เก็บ token ลง store
    (authStore as any).refreshToken = refreshToken; // ถ้า store ของคุณรองรับ
    authStore.user = userData;
    authStore.isAuthenticated = true;
    authStore.ready = true;

    // ✅ 2) แค่ set state ให้ store
    // ❌ อย่า navigate หรือ reload ที่นี่
    // ❌ อย่าเรียก keycloak.init() ซ้ำ
  }
});
</script>

<template>
  <UContainer class="flex flex-col items-center justify-center h-screen text-center">
    <UCard class="p-6 w-[280px] text-center">
      <div class="animate-pulse text-lg font-semibold text-gray-700">
        🔄 {{ t("global.loading") }}
      </div>
    </UCard>
  </UContainer>
</template> -->
