import { useAuthStore } from '~/stores/auth'
import type { Permission } from '~/types/auth'

export type PermissionCheck = {
    any?: string[]
    all?: string[]
    action?: string // c | r | u | d
}

function hasAllow(allow: string, action?: string) {
    if (!action) return true // ถ้าไม่ระบุ action ให้ผ่าน
    return allow.toLowerCase().includes(action.toLowerCase())
}

export function usePermission() {
    const authStore = useAuthStore()

    const checkKey = (
        key: string,
        permissions: Permission[],
        role: string,
        action?: string
    ): boolean => {
        // 1) ตรวจ role ตรงๆ
        if (role === key) return true

        // 2) ตรวจ resource + allow (crud)
        return permissions.some(
            (p: Permission) =>
                p.resource === key && hasAllow(p.allow, action)
        )
    }

    const hasPermission = (check: PermissionCheck): boolean => {
        const user = authStore.user
        if (!user) return false

        const permissions = user.permissions || []
        const role = user.role || ''

        const anyPass =
            !check.any ||
            check.any.some((key) => checkKey(key, permissions, role, check.action))
        const allPass =
            !check.all ||
            check.all.every((key) => checkKey(key, permissions, role, check.action))

        return anyPass && allPass
    }

    return { hasPermission }
}


// ✅ OR เฉพาะ role หรือ resource
// hasPermission({ any: ['role_administrator', 'menu_dashboard'] })

// ✅ AND ต้องมีทั้ง role และ resource
// hasPermission({ all: ['role_administrator', 'menu_dashboard'] })

// ✅ ตรวจ CRUD action เช่น แก้ไข
// hasPermission({ all: ['menu_dashboard'], action: 'u' })

// ✅ AND + OR + CRUD
// hasPermission({
//   any: ['role_manager', 'role_administrator'],
//   all: ['menu_dashboard'],
//   action: 'r' // ต้องมีสิทธิอ่าน
// })
