// app/plugins/mqtt.client.ts
import mqtt from 'mqtt'

export default defineNuxtPlugin(() => {
  const { public: cfg } = useRuntimeConfig()

  const client = mqtt.connect(cfg.mqttURL, {
    clientId: 'nuxt-client_' + Math.random().toString(16).substring(2, 8),
    username: cfg.mqttUsername,
    password: cfg.mqttPassword,
    rejectUnauthorized: false
  })

  client.on('connect', () => console.log('✅ [MQTT] Connected to broker'))
  client.on('reconnect', () => console.warn('🔄 [MQTT] Reconnecting...'))
  client.on('error', (error) => console.error('❌ [MQTT] Connection error:', error))
  client.on('close', () => console.warn('🔌 [MQTT] Connection closed'))
  client.on('offline', () => console.warn('⚠️ [MQTT] Client is offline'))
  client.on('end', () => console.log('🔚 [MQTT] Client ended'))

  return { provide: { mqtt: client } }
})
