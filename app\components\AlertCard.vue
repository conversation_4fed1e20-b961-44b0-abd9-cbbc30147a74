<template>
  <div class="map-overlay-card-Alarm" :style="{
    display: 'flex',
    gap: '10px',
    backgroundColor: alert.status ? '#00C16AD9' : '#ED4C5CD9',
    padding: '10px',
    borderRadius: '8px',
    color: 'white',
    minWidth: '250px'
  }">
    <div>
      <img v-if="alert.status" src="/images/icon_normal.png" alt="Normal" style="width: 50px; height: 50px;">
      <img v-else src="/images/icon_alert.png" alt="Alert" style="width: 50px; height: 50px;">
    </div>
    <div style="display: flex; flex-direction: column; align-items: flex-start; font-size: 10px;">
      <p>ตรวจพบ : {{ alert.detectedEvent }}</p>
      <p>วัตถุ : {{ alert.detectedObject }}</p>
      <p>กล้อง : {{ alert.description }}</p>
      <p>วันที่ : {{ formatDateTime(alert.dateTime) }}</p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    alert: {
      type: Object,
      required: true,
      validator: (value) => {
        return 'id' in value &&
               'status' in value &&
               'description' in value &&
               'dateTime' in value &&
               'detectedEvent' in value &&
               'detectedObject' in value;
      }
    }
  },
  methods: {
    formatDateTime(isoString) {
      try {
        const date = new Date(isoString);
        if (isNaN(date.getTime())) {
          return isoString;
        }
        const options = {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false 
        };
        return `วันที่ : ${date.toLocaleDateString('th-TH', { year: 'numeric', month: '2-digit', day: '2-digit' })} เวลา: ${date.toLocaleTimeString('th-TH', { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false })} น.`;
      } catch (e) {
        console.error("Failed to parse date:", isoString, e);
        return isoString;
      }
    }
  }
};
</script>

<style scoped>
.map-overlay-card-Alarm p {
  margin: 0;
  line-height: 1.4;
}
</style>