<!-- app/pages/index.vue -->
<script setup lang="ts">
const { t } = useI18n()
definePageMeta({
  layout: 'landing',
  public: true
})
</script>

<template>
  <section class="text-center py-32 bg-gradient-to-b from-gray-50 to-white dark:from-gray-950 dark:to-gray-900">
    <h1 class="text-4xl font-bold mb-4">
      {{ t("landing.klynx.nextgen") }}
    </h1>
    <p class="text-gray-600 dark:text-gray-400 mb-6 max-w-xl mx-auto">
      {{ t("landing.klynx.description") }}
    </p>
    <UButton size="xl" to="/login">{{ t("landing.klynx.getstart") }}</UButton>
  </section>
</template>
