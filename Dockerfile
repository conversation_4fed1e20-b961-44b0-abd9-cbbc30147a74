# --- Stage 1: Build the application ---
FROM docker.io/oven/bun:1.2.19-debian AS builder

# ตั้งค่า Working Directory
WORKDIR /app

# คัดลอกไฟล์ package.json และ bun.lockb เพื่อให้ Bun ติดตั้ง Dependencies ได้อย่างมีประสิทธิภาพ
COPY package.json ./

# ติดตั้ง Git (จำเป็นสำหรับบาง Nuxt modules หรือ build processes เช่น Nuxt Icon)
# และลบ cache ของ apt เพื่อลดขนาด Image ใน Stage นี้
RUN apt-get update && apt-get install -y git && rm -rf /var/lib/apt/lists/*

# ติดตั้ง project dependencies ด้วย Bun
# --frozen-lockfile ใช้เพื่อให้แน่ใจว่า dependencies ถูกติดตั้งตาม bun.lockb เป๊ะๆ
RUN bun install --frozen-lockfile

# คัดลอก source code ที่เหลือทั้งหมด
COPY . .

# รันคำสั่ง Build ของ Nuxt.js
# คำสั่งนี้จะสร้าง Production-ready assets ในโฟลเดอร์ .output/
RUN bun run build

# --- Stage 2: Serve the application ---
# ใช้ Bun image ที่มี Alpine Linux เป็น base สำหรับ Runtime Stage เพื่อขนาดที่เล็กที่สุด
FROM docker.io/oven/bun:1.2.19-alpine

# ตั้งค่า Working Directory
WORKDIR /app

# ตั้งค่า Environment Variable สำหรับ Production
ENV NODE_ENV=production

# คัดลอก Output ที่ Build แล้วจาก Stage 'builder'
# .output/server/index.mjs คือไฟล์หลักสำหรับ SSR
# .output/public คือไฟล์ static assets
COPY --from=builder /app/.output/server/index.mjs ./server/index.mjs
COPY --from=builder /app/.output/public ./public

# คัดลอก node_modules ที่จำเป็นสำหรับ Production จาก Stage 'builder'
# Nuxt 4 (Nitro) มักจะรวม dependencies ที่จำเป็นไว้ใน .output/server/node_modules
# แต่บางครั้งอาจต้องคัดลอกเพิ่มหากมี dependencies นอกเหนือจากนั้น
# ในกรณีของ Nuxt 4 (Nitro), .output/server/index.mjs จะจัดการการโหลด dependencies เอง
# ดังนั้นเราจะคัดลอกเฉพาะที่จำเป็นจริงๆ หรือถ้าไม่แน่ใจก็คัดลอกทั้งหมดจาก .output/server/node_modules
COPY --from=builder /app/.output/server/node_modules ./server/node_modules

# Expose port ที่แอปพลิเคชันจะรัน (ค่าเริ่มต้น 3000)
EXPOSE 3000

# คำสั่งเริ่มต้นสำหรับรันแอปพลิเคชันใน Production
# 'bun start' จะรัน script 'start' ที่อยู่ใน package.json ของคุณ
# ซึ่งโดยปกติแล้วจะเป็น 'node .output/server/index.mjs' สำหรับ Nuxt SSR
CMD ["bun", "start"]
