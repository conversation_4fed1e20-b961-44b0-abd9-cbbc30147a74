type AlarmWebItem = {
  owner: string
  licensePlate: string
  licensePlateRegion: string
  event: string
  device: string
};

export interface EventRow {
  alarmWeb: AlarmWebItem[]
  analytics: {
    licensePlateRecognition: {
      licensePlate: {
        plate: {
          value: string
        }
        region: {
          value: string
        }
      }
      image: string
    }
  }
}

export interface Scenario {
  id: string
  name: string
}

import type { EventRow, Scenario, User } from '~/types'
